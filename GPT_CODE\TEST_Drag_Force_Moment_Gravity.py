import numpy as np
import matplotlib.pyplot as plt

# --- 1. 定义更完整的物理参数 (参考论文表3) ---
# 航行器几何与质量参数
VEHICLE_HEIGHT = 0.3      # 航行器总高度 H (m)
VEHICLE_RADIUS = 0.04     # 航行器半径 R (m)
MASS = 2.0                # 航行器质量 (kg)

# 环境参数
RHO_WATER = 1025          # 水的密度 (kg/m^3)
GRAVITY = 9.81            # 重力加速度 (m/s^2)

# 阻力与浮力相关计算
# 使用论文中的 Zw = -129，这意味着 Fd = ks * Zw * w|w| (负号已包含在Zw中)
# 为了更清晰，我们用正的阻力系数，在公式中加负号
Zw_coeff = 129.0
VEHICLE_VOLUME = np.pi * VEHICLE_RADIUS**2 * VEHICLE_HEIGHT
MAX_BUOYANCY_FORCE = RHO_WATER * GRAVITY * VEHICLE_VOLUME

# 仿真参数
SIM_TIME = 3.0   # 总模拟时间 (s)
DT = 0.001       # 时间步长 (s) - 需要更小以保证动态仿真的精度

# --- 2. 核心计算函数 (与之前相同或稍作修改) ---

def calculate_ks_and_H_bar(z_center, H):
    z_bottom = z_center - H / 2
    z_top = z_center + H / 2
    
    if z_bottom >= 0: submerged_height = 0
    elif z_top <= 0: submerged_height = H
    else: submerged_height = -z_bottom
    
    H_bar = submerged_height / H
    ks = np.clip(H_bar, 0, 1)
    return H_bar, ks

def calculate_buoyancy(H_bar):
    """根据浸没比例计算浮力"""
    return MAX_BUOYANCY_FORCE * H_bar

def calculate_z_drag(velocity_z, ks, z_drag_coeff):
    """计算Z轴阻力。方向与速度相反。"""
    if ks == 0: return 0.0
    # 速度为负(向下)，阻力为正(向上)
    return -ks * z_drag_coeff * velocity_z * np.abs(velocity_z)

# --- 3. 运行动态仿真 ---

time_steps = np.arange(0, SIM_TIME, DT)
# 状态变量：位置, 速度, 加速度
z_pos = np.zeros_like(time_steps)
z_vel = np.zeros_like(time_steps)
z_acc = np.zeros_like(time_steps)

# 力分量
force_g = -MASS * GRAVITY  # 重力是恒定的
force_b = np.zeros_like(time_steps)
force_d = np.zeros_like(time_steps)
force_net = np.zeros_like(time_steps)

# 初始条件：从空中静止释放
z_pos[0] = 2.0  # 从2米高处释放
z_vel[0] = 0.0

# 核心动态仿真循环
for i in range(1, len(time_steps)):
    # 获取上一步的状态
    z_prev = z_pos[i-1]
    v_prev = z_vel[i-1]

    # 1. 根据上一步的位置计算与位置相关的力
    H_bar, ks = calculate_ks_and_H_bar(z_prev, VEHICLE_HEIGHT)
    force_b[i] = calculate_buoyancy(H_bar)

    # 2. 根据上一步的速度计算与速度相关的力
    force_d[i] = calculate_z_drag(v_prev, ks, Zw_coeff)
    
    # 3. 计算净力
    force_net[i] = force_g + force_b[i] + force_d[i]
    
    # 4. 根据牛顿第二定律计算加速度
    z_acc[i] = force_net[i] / MASS
    
    # 5. 用欧拉法积分更新速度和位置
    z_vel[i] = v_prev + z_acc[i] * DT
    z_pos[i] = z_prev + z_vel[i] * DT # 使用新速度更新位置会更稳定

# --- 4. 可视化结果 ---

fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 15), sharex=True)

# 图1: 位置 vs. 时间
ax1.plot(time_steps, z_pos, label='Z-position', color='blue')
ax1.axhline(0, color='cyan', linestyle='--', label='Water Surface')
ax1.set_ylabel('Position (m)')
ax1.set_title('HAUV Water Entry Simulation (Driven by Gravity)')
ax1.grid(True)
ax1.legend()

# 图2: 速度 vs. 时间
ax2.plot(time_steps, z_vel, label='Z-velocity', color='green')
ax2.set_ylabel('Velocity (m/s)')
ax2.grid(True)
ax2.legend()

# 图3: 力 vs. 时间
ax3.plot(time_steps, np.full_like(time_steps, force_g), '--', label='Gravity Force', color='black')
ax3.plot(time_steps, force_b, label='Buoyancy Force', color='orange')
ax3.plot(time_steps, force_d, label='Drag Force', color='red')
ax3.plot(time_steps, force_net, label='Net Force', color='purple', linewidth=2)
ax3.set_xlabel('Time (s)')
ax3.set_ylabel('Forces (N)')
ax3.grid(True)
ax3.legend()

plt.tight_layout()
plt.show()