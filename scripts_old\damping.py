"""
阻尼力计算模块
根据物体在水中的位置计算线性和角度阻尼系数
用于模拟水下物体受到的流体阻尼效应

重构版本：同时支持 ActionGraph 模式和纯 Python 调用
"""
# TODO 是否需要加深数据的维度添加精确性，且对于不规则体积处理部分还是需要加强
# ==================== 纯 Python 接口 ====================

def calculate_damping_coefficients(z_position, height, max_damping, air_damping=0.01):
    """
    根据物体位置计算阻尼系数（纯 Python 版本）

    阻尼系数随物体浸没深度变化：
    - 完全在水面上方：最小阻尼（空气阻尼）
    - 完全在水面下方：最大阻尼（水阻尼）
    - 部分浸没：按浸没比例线性插值

    参数:
        z_position: 物体在Z轴的位置 (m)
        height: 浮体的高度 (m)
        max_damping: 最大阻尼系数（完全浸没时）
        air_damping: 空气阻尼系数，默认 0.01

    返回:
        dict: 阻尼系数结果
            {
                'linear_damping': float,      # 线性阻尼系数
                'angular_damping': float,     # 角度阻尼系数
                'damping_ratio': float,       # 阻尼比例 (0.0-1.0)
                'submerged_percentage': float # 浸没百分比 (0.0-1.0)
            }
    """
    half_height = height / 2  # 浮体半高

    # 计算浸没百分比
    # 公式：(-z_pos / height + 0.5)
    # 当z_pos = -height/2时（完全浸没），百分比 = 1.0
    # 当z_pos = height/2时（完全在水面上），百分比 = 0.0
    submerged_percentage = max(0.0, min(1.0, -z_position / height + 0.5))

    # 根据位置计算阻尼系数
    if z_position >= half_height:
        # 物体完全在水面上方：使用最小阻尼（空气阻尼）
        damping = air_damping
        damping_ratio = 0.0
    elif z_position <= -half_height:
        # 物体完全在水面下方：使用最大阻尼（水阻尼）
        damping = max_damping
        damping_ratio = 1.0
    else:
        # 物体部分浸没：按浸没比例计算阻尼
        damping_ratio = submerged_percentage
        damping = air_damping + (max_damping - air_damping) * damping_ratio

    return {
        'linear_damping': damping,
        'angular_damping': damping,  # 通常线性和角度阻尼相同
        'damping_ratio': damping_ratio,
        'submerged_percentage': submerged_percentage
    }

def calculate_drag_force(velocity, damping_coefficient, drag_model='quadratic'):
    """
    根据速度和阻尼系数计算阻力（纯 Python 版本）

    参数:
        velocity: 速度向量 [vx, vy, vz] (m/s)
        damping_coefficient: 阻尼系数
        drag_model: 阻力模型，'linear' 或 'quadratic'

    返回:
        list: 阻力向量 [fx, fy, fz] (N)
    """
    if isinstance(velocity, (int, float)):
        # 单轴速度
        if drag_model == 'quadratic':
            drag = -velocity * abs(velocity) * damping_coefficient
        else:  # linear
            drag = -velocity * damping_coefficient
        return drag
    else:
        # 速度向量
        drag_force = []
        for v in velocity:
            if drag_model == 'quadratic':
                drag = -v * abs(v) * damping_coefficient
            else:  # linear
                drag = -v * damping_coefficient
            drag_force.append(drag)
        return drag_force

def calculate_environmental_damping(z_position, height, velocity, max_damping=50.0, air_damping=0.01):
    """
    计算环境阻尼力（综合函数）

    参数:
        z_position: 物体Z轴位置 (m)
        height: 物体高度 (m)
        velocity: 速度向量 [vx, vy, vz] (m/s)
        max_damping: 最大阻尼系数
        air_damping: 空气阻尼系数

    返回:
        dict: 完整的阻尼计算结果
            {
                'damping_coefficients': dict,  # 阻尼系数信息
                'drag_force': list,           # 阻力向量
                'total_damping': float        # 总阻尼系数
            }
    """
    # 计算阻尼系数
    damping_info = calculate_damping_coefficients(z_position, height, max_damping, air_damping)

    # 计算阻力
    drag_force = calculate_drag_force(velocity, damping_info['linear_damping'], 'quadratic')

    return {
        'damping_coefficients': damping_info,
        'drag_force': drag_force,
        'total_damping': damping_info['linear_damping']
    }

# ==================== ActionGraph 兼容接口 ====================

def setup(db):
    """
    初始化函数（ActionGraph 兼容）
    设置必要的变量或数据结构

    参数:
        db: 数据库对象，用于存储和传递数据

    注意:
        此模块不需要特殊的初始化操作
    """
    pass  # 此情况下不需要初始化操作

def compute(db):
    """
    根据物体位置计算阻尼系数（ActionGraph 兼容版本）

    参数:
        db: 数据库对象，包含输入和输出数据
            输入参数:
                z_position: 物体在Z轴的位置 (m)
                max_damping: 最大阻尼系数（完全浸没时）
                floating_obj_height: 浮体的高度 (m)
            输出参数:
                linear_damping: 线性阻尼系数
                angular_damping: 角度阻尼系数
    """
    # 获取输入参数
    z_pos = db.inputs.z_position
    max_damp = db.inputs.max_damping
    height = db.inputs.floating_obj_height

    # 使用纯 Python 函数计算
    result = calculate_damping_coefficients(z_pos, height, max_damp)

    # 输出结果
    db.outputs.linear_damping = result['linear_damping']
    db.outputs.angular_damping = result['angular_damping']

# ==================== 测试和演示函数 ====================

def test_damping_module():
    """测试阻尼计算模块"""
    print("🧪 测试阻尼计算模块 (damping.py)")
    print("="*50)

    print("📖 模块功能说明:")
    print("   - 根据浸没深度计算阻尼系数")
    print("   - 支持线性和二次阻力模型")
    print("   - 区分空气阻尼和水阻尼")
    print("   - 提供环境阻尼的综合计算")
    print("   - 同时支持 ActionGraph 和纯 Python 调用")

    # 1. 测试不同位置的阻尼系数
    print(f"\n1️⃣ 测试不同位置的阻尼系数:")
    height = 2.0  # 2米高的物体
    max_damping = 50.0

    test_positions = [
        {'z': 2.0, 'desc': '完全在水面上方'},
        {'z': 0.5, 'desc': '部分在水面上方'},
        {'z': 0.0, 'desc': '中心在水面'},
        {'z': -0.5, 'desc': '部分浸没'},
        {'z': -1.0, 'desc': '完全浸没'},
        {'z': -3.0, 'desc': '深度浸没'}
    ]

    for pos in test_positions:
        result = calculate_damping_coefficients(pos['z'], height, max_damping)

        print(f"   {pos['desc']} (z={pos['z']}m):")
        print(f"     线性阻尼: {result['linear_damping']:.2f}")
        print(f"     浸没百分比: {result['submerged_percentage']:.2f}")
        print(f"     阻尼比例: {result['damping_ratio']:.2f}")

    # 2. 测试阻力计算
    print(f"\n2️⃣ 测试阻力计算:")
    damping_coeff = 25.0  # 中等阻尼系数

    test_velocities = [
        {'v': [0.5, 0, 0], 'desc': 'X方向运动'},
        {'v': [0, 0.3, 0], 'desc': 'Y方向运动'},
        {'v': [0, 0, -0.8], 'desc': 'Z方向下沉'},
        {'v': [0.2, 0.3, -0.5], 'desc': '复合运动'}
    ]

    for vel_test in test_velocities:
        # 线性阻力模型
        linear_drag = calculate_drag_force(vel_test['v'], damping_coeff, 'linear')
        # 二次阻力模型
        quad_drag = calculate_drag_force(vel_test['v'], damping_coeff, 'quadratic')

        print(f"   {vel_test['desc']} 速度={vel_test['v']}:")
        print(f"     线性阻力: [{linear_drag[0]:.1f}, {linear_drag[1]:.1f}, {linear_drag[2]:.1f}] N")
        print(f"     二次阻力: [{quad_drag[0]:.1f}, {quad_drag[1]:.1f}, {quad_drag[2]:.1f}] N")

def test_environmental_damping():
    """测试环境阻尼综合计算"""
    print(f"\n3️⃣ 测试环境阻尼综合计算:")

    # ROV 参数
    height = 2.0
    velocity = [0.2, 0.1, -0.5]  # 复合运动

    # 测试不同深度的环境阻尼
    test_depths = [-0.5, -1.0, -2.0, -3.0]

    for depth in test_depths:
        result = calculate_environmental_damping(depth, height, velocity)

        damping_info = result['damping_coefficients']
        drag_force = result['drag_force']

        print(f"   深度 {depth}m:")
        print(f"     总阻尼系数: {result['total_damping']:.2f}")
        print(f"     浸没百分比: {damping_info['submerged_percentage']:.2f}")
        print(f"     阻力向量: [{drag_force[0]:.1f}, {drag_force[1]:.1f}, {drag_force[2]:.1f}] N")

def demo_usage_examples():
    """演示使用示例"""
    print(f"\n📚 使用示例:")
    print("="*30)

    print("\n💡 示例 1: 计算阻尼系数")
    print("```python")
    print("from scripts.damping import calculate_damping_coefficients")
    print("")
    print("# 计算 2m 高物体在 1m 深度的阻尼")
    print("result = calculate_damping_coefficients(")
    print("    z_position=-1.0,    # 1米深度")
    print("    height=2.0,         # 2米高")
    print("    max_damping=50.0    # 最大阻尼系数")
    print(")")
    print("print(f'线性阻尼: {result[\"linear_damping\"]:.2f}')")
    print("```")

    # 实际运行示例
    result = calculate_damping_coefficients(z_position=-1.0, height=2.0, max_damping=50.0)
    print(f"➡️ 输出: 线性阻尼: {result['linear_damping']:.2f}")

    print("\n💡 示例 2: 计算阻力")
    print("```python")
    print("from scripts.damping import calculate_drag_force")
    print("")
    print("# 计算速度为 [0.5, 0, -0.3] 的阻力")
    print("drag = calculate_drag_force(")
    print("    velocity=[0.5, 0, -0.3],")
    print("    damping_coefficient=25.0,")
    print("    drag_model='quadratic'")
    print(")")
    print("```")

    # 实际运行示例
    drag = calculate_drag_force([0.5, 0, -0.3], 25.0, 'quadratic')
    print(f"➡️ 输出: 阻力 = [{drag[0]:.1f}, {drag[1]:.1f}, {drag[2]:.1f}] N")

    print("\n💡 示例 3: 环境阻尼综合计算")
    print("```python")
    print("from scripts.damping import calculate_environmental_damping")
    print("")
    print("result = calculate_environmental_damping(")
    print("    z_position=-1.5,")
    print("    height=2.0,")
    print("    velocity=[0.2, 0.1, -0.4]")
    print(")")
    print("```")

    # 实际运行示例
    result = calculate_environmental_damping(-1.5, 2.0, [0.2, 0.1, -0.4])
    print(f"➡️ 输出: 总阻尼系数 = {result['total_damping']:.2f}")
    print(f"➡️ 输出: 阻力向量 = {[f'{f:.1f}' for f in result['drag_force']]} N")

if __name__ == "__main__":
    """当直接运行此文件时执行测试和演示"""
    test_damping_module()
    test_environmental_damping()
    demo_usage_examples()
    print("\n✅ 阻尼计算模块测试完成！")
    print("\n🔗 相关模块:")
    print("   - buoyancy_control.py: 浮力计算")
    print("   - controller.py: PID 控制")
    print("   - rov_physics_unified.py: 统一物理引擎")
    