SHELL=/bin/bash
ROS_VERSION=2
SESSION_MANAGER=local/lab317:@/tmp/.ICE-unix/3309,unix/lab317:/tmp/.ICE-unix/3309
QT_ACCESSIBILITY=1
COLORTERM=truecolor
XDG_CONFIG_DIRS=/etc/xdg/xdg-ubuntu:/etc/xdg
SSH_AGENT_LAUNCHER=gnome-keyring
XDG_MENU_PREFIX=gnome-
no_proxy=localhost,127.0.0.1,***********/16,10.0.0.0/8,**********/12,**********/16,::1
TERM_PROGRAM_VERSION=1.86.2
GNOME_DESKTOP_SESSION_ID=this-is-deprecated
GTK_IM_MODULE=ibus
CONDA_EXE=/home/<USER>/miniconda3/bin/conda
ROS_PYTHON_VERSION=3
EXP_PATH=/home/<USER>/isaac-sim/apps
LANGUAGE=zh_CN:zh
GNOME_SHELL_SESSION_MODE=ubuntu
SSH_AUTH_SOCK=/run/user/1000/keyring/ssh
ISAACSIM_PATH=/home/<USER>/isaac-sim
XMODIFIERS=@im=ibus
DESKTOP_SESSION=ubuntu
GTK_MODULES=gail:atk-bridge
DBUS_STARTER_BUS_TYPE=session
PWD=/home/<USER>/isaac-sim
XDG_SESSION_DESKTOP=ubuntu
LOGNAME=lab317
XDG_SESSION_TYPE=x11
CONDA_PREFIX=/home/<USER>/miniconda3/envs/isaac
GPG_AGENT_INFO=/run/user/1000/gnupg/S.gpg-agent:0:1
SYSTEMD_EXEC_PID=3309
XAUTHORITY=/run/user/1000/gdm/Xauthority
VSCODE_GIT_ASKPASS_NODE=/usr/share/code/code
OMNI_KIT_ACCEPT_EULA=YES
WINDOWPATH=2
HOME=/home/<USER>
USERNAME=lab317
LANG=zh_CN.UTF-8
ISAACSIM_PYTHON_EXE=/home/<USER>/isaac-sim/python.sh
LS_COLORS=rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:
XDG_CURRENT_DESKTOP=Unity
VTE_VERSION=6800
CONDA_PROMPT_MODIFIER=(isaac) 
AMENT_PREFIX_PATH=/opt/ros/humble
GIT_ASKPASS=/usr/share/code/resources/app/extensions/git/dist/askpass.sh
GNOME_TERMINAL_SCREEN=/org/gnome/Terminal/screen/6c8408f6_edaa_437a_acca_d7e5829442cc
https_proxy=http://127.0.0.1:7897/
CHROME_DESKTOP=code-url-handler.desktop
VSCODE_GIT_ASKPASS_EXTRA_ARGS=
LESSCLOSE=/usr/bin/lesspipe %s %s
XDG_SESSION_CLASS=user
PYTHONPATH=/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages:/home/<USER>/isaac-sim/../../..//opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages:/home/<USER>/isaac-sim/python_packages:/home/<USER>/isaac-sim/exts/isaacsim.simulation_app:/home/<USER>/isaac-sim/extsDeprecated/omni.isaac.kit:/home/<USER>/isaac-sim/kit/kernel/py:/home/<USER>/isaac-sim/kit/plugins/bindings-python:/home/<USER>/isaac-sim/exts/isaacsim.robot_motion.lula/pip_prebundle:/home/<USER>/isaac-sim/exts/isaacsim.asset.exporter.urdf/pip_prebundle:/home/<USER>/isaac-sim/extscache/omni.kit.pip_archive-0.0.0+d02c707b.lx64.cp310/pip_prebundle:/home/<USER>/isaac-sim/exts/omni.isaac.core_archive/pip_prebundle:/home/<USER>/isaac-sim/exts/omni.isaac.ml_archive/pip_prebundle:/home/<USER>/isaac-sim/exts/omni.pip.compute/pip_prebundle:/home/<USER>/isaac-sim/exts/omni.pip.cloud/pip_prebundle:/home/<USER>/isaac-sim/../../..//opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages:/home/<USER>/isaac-sim/../../..//opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages:/home/<USER>/isaac-sim/python_packages:/home/<USER>/isaac-sim/exts/isaacsim.simulation_app:/home/<USER>/isaac-sim/extsDeprecated/omni.isaac.kit:/home/<USER>/isaac-sim/kit/kernel/py:/home/<USER>/isaac-sim/kit/plugins/bindings-python:/home/<USER>/isaac-sim/exts/isaacsim.robot_motion.lula/pip_prebundle:/home/<USER>/isaac-sim/exts/isaacsim.asset.exporter.urdf/pip_prebundle:/home/<USER>/isaac-sim/extscache/omni.kit.pip_archive-0.0.0+d02c707b.lx64.cp310/pip_prebundle:/home/<USER>/isaac-sim/exts/omni.isaac.core_archive/pip_prebundle:/home/<USER>/isaac-sim/exts/omni.isaac.ml_archive/pip_prebundle:/home/<USER>/isaac-sim/exts/omni.pip.compute/pip_prebundle:/home/<USER>/isaac-sim/exts/omni.pip.cloud/pip_prebundle:/home/<USER>/isaac-sim/kit/python/lib/python3.10:/home/<USER>/isaac-sim/kit/python/lib/python3.10/site-packages:/home/<USER>/isaac-sim/python_packages:/home/<USER>/isaac-sim/exts/isaacsim.simulation_app:/home/<USER>/isaac-sim/extsDeprecated/omni.isaac.kit:/home/<USER>/isaac-sim/kit/kernel/py:/home/<USER>/isaac-sim/kit/plugins/bindings-python:/home/<USER>/isaac-sim/exts/isaacsim.robot_motion.lula/pip_prebundle:/home/<USER>/isaac-sim/exts/isaacsim.asset.exporter.urdf/pip_prebundle:/home/<USER>/isaac-sim/extscache/omni.kit.pip_archive-0.0.0+d02c707b.lx64.cp310/pip_prebundle:/home/<USER>/isaac-sim/exts/omni.isaac.core_archive/pip_prebundle:/home/<USER>/isaac-sim/exts/omni.isaac.ml_archive/pip_prebundle:/home/<USER>/isaac-sim/exts/omni.pip.compute/pip_prebundle:/home/<USER>/isaac-sim/exts/omni.pip.cloud/pip_prebundle
TERM=xterm-256color
LESSOPEN=| /usr/bin/lesspipe %s
USER=lab317
NO_PROXY=localhost,127.0.0.1,***********/16,10.0.0.0/8,**********/12,**********/16,::1
VSCODE_GIT_IPC_HANDLE=/run/user/1000/vscode-git-84e07a3c3e.sock
GNOME_TERMINAL_SERVICE=:1.376
CONDA_SHLVL=2
DISPLAY=:1
SHLVL=2
HTTPS_PROXY=http://127.0.0.1:7897/
HTTP_PROXY=http://127.0.0.1:7897/
QT_IM_MODULE=ibus
DBUS_STARTER_ADDRESS=unix:path=/run/user/1000/bus,guid=633ca2b58a23c6765d155ff8688c16b7
http_proxy=http://127.0.0.1:7897/
CONDA_PYTHON_EXE=/home/<USER>/miniconda3/bin/python
LD_LIBRARY_PATH=/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/home/<USER>/isaac-sim/../../..//opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/home/<USER>/isaac-sim/.:/home/<USER>/isaac-sim/exts/omni.usd.schema.isaac/plugins/IsaacSensorSchema/lib:/home/<USER>/isaac-sim/exts/omni.usd.schema.isaac/plugins/RangeSensorSchema/lib:/home/<USER>/isaac-sim/exts/isaacsim.robot_motion.lula/pip_prebundle:/home/<USER>/isaac-sim/exts/isaacsim.asset.exporter.urdf/pip_prebundle:/home/<USER>/isaac-sim/kit:/home/<USER>/isaac-sim/kit/kernel/plugins:/home/<USER>/isaac-sim/kit/libs/iray:/home/<USER>/isaac-sim/kit/plugins:/home/<USER>/isaac-sim/kit/plugins/bindings-python:/home/<USER>/isaac-sim/kit/plugins/carb_gfx:/home/<USER>/isaac-sim/kit/plugins/rtx:/home/<USER>/isaac-sim/kit/plugins/gpu.foundation:/home/<USER>/isaac-sim/../../..//opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/home/<USER>/isaac-sim/../../..//opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/home/<USER>/isaac-sim/.:/home/<USER>/isaac-sim/exts/omni.usd.schema.isaac/plugins/IsaacSensorSchema/lib:/home/<USER>/isaac-sim/exts/omni.usd.schema.isaac/plugins/RangeSensorSchema/lib:/home/<USER>/isaac-sim/exts/isaacsim.robot_motion.lula/pip_prebundle:/home/<USER>/isaac-sim/exts/isaacsim.asset.exporter.urdf/pip_prebundle:/home/<USER>/isaac-sim/kit:/home/<USER>/isaac-sim/kit/kernel/plugins:/home/<USER>/isaac-sim/kit/libs/iray:/home/<USER>/isaac-sim/kit/plugins:/home/<USER>/isaac-sim/kit/plugins/bindings-python:/home/<USER>/isaac-sim/kit/plugins/carb_gfx:/home/<USER>/isaac-sim/kit/plugins/rtx:/home/<USER>/isaac-sim/kit/plugins/gpu.foundation:/home/<USER>/isaac-sim/.:/home/<USER>/isaac-sim/exts/omni.usd.schema.isaac/plugins/IsaacSensorSchema/lib:/home/<USER>/isaac-sim/exts/omni.usd.schema.isaac/plugins/RangeSensorSchema/lib:/home/<USER>/isaac-sim/exts/isaacsim.robot_motion.lula/pip_prebundle:/home/<USER>/isaac-sim/exts/isaacsim.asset.exporter.urdf/pip_prebundle:/home/<USER>/isaac-sim/kit:/home/<USER>/isaac-sim/kit/kernel/plugins:/home/<USER>/isaac-sim/kit/libs/iray:/home/<USER>/isaac-sim/kit/plugins:/home/<USER>/isaac-sim/kit/plugins/bindings-python:/home/<USER>/isaac-sim/kit/plugins/carb_gfx:/home/<USER>/isaac-sim/kit/plugins/rtx:/home/<USER>/isaac-sim/kit/plugins/gpu.foundation
XDG_RUNTIME_DIR=/run/user/1000
VK_ICD_FILENAMES=/usr/share/vulkan/icd.d/nvidia_icd.json
ROS_LOCALHOST_ONLY=0
CONDA_DEFAULT_ENV=isaac
ALL_PROXY=socks://127.0.0.1:7897/
VSCODE_GIT_ASKPASS_MAIN=/usr/share/code/resources/app/extensions/git/dist/askpass-main.js
XDG_DATA_DIRS=/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop
GDK_BACKEND=x11
ISAAC_PATH=/home/<USER>/isaac-sim
all_proxy=socks://127.0.0.1:7897/
PATH=/home/<USER>/IsaacLab/_isaac_sim/kit/python/bin:/opt/ros/humble/bin:/home/<USER>/miniconda3/envs/isaac/bin:/home/<USER>/miniconda3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin
GDMSESSION=ubuntu
ORIGINAL_XDG_CURRENT_DESKTOP=ubuntu:GNOME
DBUS_SESSION_BUS_ADDRESS=unix:path=/run/user/1000/bus,guid=633ca2b58a23c6765d155ff8688c16b7
CONDA_PREFIX_1=/home/<USER>/miniconda3
ROS_DISTRO=humble
CARB_APP_PATH=/home/<USER>/isaac-sim/kit
TERM_PROGRAM=vscode
_=/usr/bin/printenv
