import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

plt.rcParams['axes.unicode_minus'] = False

x = np.linspace(0, 1, 100)

# 曲线的拟合数学表达
y1 = 1 - (1-x)**3           # 蓝色：上凸到1
y2 = x**3                   # 橙色：缓慢上升
y3 = x**2 * (3 - 2*x)       # 棕色：凸曲线
y4 = 4 * x**2 * (1-x)       # 紫色：先升后降, 峰值在 x=2/3

plt.plot(x, y1, color='teal', label=r'$k_{zz}, k_{rr}$', linewidth=2)
plt.plot(x, y2, color='darkorange', label=r'$k_{xx}, k_{yy}$', linewidth=2)
plt.plot(x, y3, color='peru', label=r'$k_{pp}, k_{qq}$', linewidth=2)
plt.plot(x, y4, color='purple', label=r'$k_{xq}, k_{yp}$', linewidth=2)

plt.xlabel(r'$\overline{H}$')
plt.ylabel('Loss coefficients')
plt.show()