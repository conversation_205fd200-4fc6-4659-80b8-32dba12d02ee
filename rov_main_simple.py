#!/usr/bin/env python3
"""
ROV仿真主程序 - 简化版本，使用配置文件
"""

# Isaac Sim标准初始化代码
from isaacsim import SimulationApp
simulation_app = SimulationApp({"headless": False})

import os
import carb
from rov_standalone import ROVSimulation
from rov_config import ROVConfig, ROVPresets


def main():
    """主函数 - 使用配置文件的简化版本"""
    
    print("🚀 ROV水下仿真启动")
    print("=" * 50)
    
    # ========================================
    # 🎛️ 选择配置方案（在这里修改）
    # ========================================
    
    # 方案1: 使用默认配置
    # config = ROVConfig.get_config_dict()
    
    # 方案2: 使用预设配置（推荐）
    config = ROVPresets.get_floating_cube()    # 上浮方块
    # config = ROVPresets.get_sinking_cube()   # 下沉方块
    # config = ROVPresets.get_neutral_cube()   # 中性浮力
    # config = ROVPresets.get_seawater_test()  # 海水测试
    
    # 方案3: 自定义配置
    # config = ROVConfig.get_config_dict()
    # config.update({
    #     'object_mass': 0.3,        # 自定义质量
    #     'water_density': 1.0,      # 自定义水密度
    #     'debug_mode': True         # 开启调试
    # })
    
    # ========================================
    # 🔍 显示配置信息
    # ========================================
    
    object_density = config['object_mass'] / config['object_volume']
    water_density = config['water_density']
    theoretical_buoyancy = water_density * config['object_volume'] * config['gravity']
    theoretical_weight = config['object_mass'] * config['gravity']
    net_force = theoretical_buoyancy - theoretical_weight
    
    print(f"📊 当前配置:")
    print(f"   物体: 质量={config['object_mass']}kg, 体积={config['object_volume']}m³")
    print(f"   密度: 物体={object_density:.2f}kg/m³, 水={water_density:.2f}kg/m³")
    print(f"   理论力: 浮力={theoretical_buoyancy:.2f}N↑, 重力={theoretical_weight:.2f}N↓")
    print(f"   净力: {net_force:.2f}N ({'向上' if net_force > 0 else '向下' if net_force < 0 else '平衡'})")
    print(f"   预期行为: {'上浮⬆️' if object_density < water_density else '下沉⬇️' if object_density > water_density else '悬浮⚖️'}")
    print()
    
    # ========================================
    # 🚀 启动仿真
    # ========================================
    
    # 创建ROV仿真实例
    rov_sim = ROVSimulation()
    
    # 应用配置
    rov_sim.apply_config(config)
    
    # 加载USD场景
    usd_path = os.path.abspath(config['usd_file_path'])
    if not rov_sim.load_usd_scene(usd_path):
        carb.log_error("无法加载USD场景，退出")
        simulation_app.close()
        return

    # 设置物理对象
    if not rov_sim.setup_physics_object():
        carb.log_error("❌ 无法设置物理对象，退出")
        simulation_app.close()
        return

    print("🎉 物理仿真启动成功，开始仿真循环...")
    print("💡 提示: 观察方块的运动行为是否符合预期")
    print("🔧 修改配置: 编辑 rov_config.py 或在本文件顶部选择不同的预设方案")
    print()

    # 仿真循环
    try:
        while simulation_app.is_running():
            rov_sim.step()
    except KeyboardInterrupt:
        print("用户中断仿真")
    except Exception as e:
        carb.log_error(f"仿真错误: {e}")
    finally:
        simulation_app.close()


if __name__ == "__main__":
    main()
