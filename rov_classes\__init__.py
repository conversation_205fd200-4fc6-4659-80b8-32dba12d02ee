# ROV仿真类模块
# 简化版本：直接导出计算函数，移除BaseNode架构

from .buoyancy_forces import calculate_buoyancy_forces
from .buoyancy_control import calculate_buoyancy_control
from .damping import calculate_damping
from .controller import PIDController, calculate_controller_output
from .linear_angular_control import calculate_thruster_forces
from .quat_to_euler import quaternion_to_euler

__all__ = [
    'calculate_buoyancy_forces',
    'calculate_buoyancy_control',
    'calculate_damping',
    'PIDController',
    'calculate_controller_output',
    'calculate_thruster_forces',
    'quaternion_to_euler'
]
