# 混合空气-水下自主飞行器(HAUV)动力学仿真代码
# 实现论文第2节的动力学模型（紧凑、可运行版本）
#
# 说明：
# - 参数值（质量、几何形状、阻尼、附加质量、螺旋桨CT/CQ等）
#   来自上传论文的表2和表3。详见聊天记录中的引用。
# - 某些建模选择进行了简化（过渡期间线性损失系数 = H，
#   rB设为零，除了在过渡期间使用简单近似）。
# - 论文中的单位和螺旋桨系数约定略有非标准；
#   此代码使用论文中的*数值*CT/CQ值，并将omega视为RPM
#   在推力公式T = CT * omega_rpm**2中（这与作者使用的表格形式匹配）。
#   如果您更喜欢SI一致的T = rho * CT_dimless * (omega_rad_s**2) * D**4，
#   请告诉我，我将交换公式并转换系数。

import numpy as np

class HAUVDynamics:
    """
    混合空气-水下自主飞行器(HAUV)动力学类

    该类实现了HAUV在空气-水界面过渡期间的完整动力学模型，
    包括：
    - 浸没比例计算
    - 附加质量和惯性矩阵
    - 流体阻尼力和力矩
    - 恢复力（重力和浮力）
    - 螺旋桨推力模型
    - 控制力和力矩分配
    """

    def __init__(self):
        """
        初始化HAUV动力学参数

        参数来源于论文表2和表3，包括：
        - 物理参数（质量、几何尺寸）
        - 流体参数（空气和水的密度）
        - 惯性参数
        - 流体动力学系数
        - 螺旋桨参数
        """
        # --- 表3参数（来自论文） ---
        self.m = 2.0                      # 质量 (kg)
        self.H = 0.3                      # 机体高度 (m)
        self.R = 0.04                     # 机体半径 (m)
        self.L = 0.4                      # 臂长 (m)
        self.rho_air = 1.29               # 空气密度 (kg/m^3)
        self.rho_water = 1025.0           # 水密度 (kg/m^3)
        self.I = np.diag([0.032,0.032,0.026])  # 惯性矩阵 Ixx, Iyy, Izz (kg*m^2)

        # 流体动力学阻尼（非线性系数乘以v*|v|）
        self.DM = np.diag([-154.0, -154.0, -129.0])   # Xu, Yv, Zw - 注意表中的符号
        self.DJ = np.diag([-10.0, -10.0, -15.0])      # Kp, Mq, Nr (旋转阻尼)

        # 附加质量(Ma)和附加惯性(Ja)基准值（论文列出负值）
        self.Ma_diag = np.array([-1.39, -1.39, -0.94])   # Xu_dot, Yv_dot, Zw_dot (kg)
        self.Ja_diag = np.array([-0.027, -0.027, -0.013])# Kp_dot, Mq_dot, Nr_dot (kg*m^2)

        # 耦合附加质量耦合项 (Xq_dot, Yp_dot)
        self.Xq_dot = -0.014
        self.Yp_dot = -0.014

        # 耦合*力*系数（用于F_add和M_add）
        self.Xq_dot_force = self.Xq_dot
        self.Yp_dot_force = self.Yp_dot

        # --- 螺旋桨参数（表2） ---
        # 论文使用15英寸螺旋桨；表中D为15英寸 -> 转换为米以便清晰
        self.D_prop_inch = 15.0
        self.D = 15.0 * 0.0254   # 米 (15英寸)

        # 作者给出CT和CQ值（我们将其作为数值常数加载）
        # 注意：论文表中的单位与rpm使用相关；这里我们保持数值
        self.CT_air = 1.653e-3      # 来自表2的数值（论文）
        self.CQ_air = 2.225e-7
        self.CT_water = 1.742e-3
        self.CQ_water = 2.322e-7

        # 转子限制 (rpm)
        self.omega_max_air = 3600.0
        self.omega_max_water = 310.0

        # d是推力分配中使用的CT/CQ比值参数（论文使用'd' = CT/CQ）
        # 我们使用空气CT/CQ定义d（如他们的仿真中使用）；用户可以按模式更改。
        # 为避免除零，使用安全保护
        self.d = (self.CT_air / self.CQ_air) if self.CQ_air!=0 else 0.0

        # 重力和体积（圆柱体）
        self.g = 9.81                     # 重力加速度 (m/s^2)
        self.V = np.pi * (self.R**2) * self.H  # 圆柱体体积 (m^3)

    def R1(self, phi, theta, psi):
        """
        从机体坐标系到地球坐标系的旋转矩阵(R1)
        使用Z-Y-X欧拉角序列(phi,theta,psi)

        参数:
            phi (float): 横滚角 (rad)
            theta (float): 俯仰角 (rad)
            psi (float): 偏航角 (rad)

        返回:
            numpy.ndarray: 3x3旋转矩阵，将机体坐标系向量转换为地球坐标系
        """
        cphi = np.cos(phi); sphi = np.sin(phi)
        ctheta = np.cos(theta); stheta = np.sin(theta)
        cpsi = np.cos(psi); spsi = np.sin(psi)
        R = np.array([
            [ctheta*cpsi, sphi*stheta*cpsi - cphi*spsi, cphi*stheta*cpsi + sphi*spsi],
            [ctheta*spsi, sphi*stheta*spsi + cphi*cpsi, cphi*stheta*spsi - sphi*cpsi],
            [-stheta,     sphi*ctheta,                 cphi*ctheta]
        ])
        return R

    def R2(self, phi, theta, psi):
        """
        角速度到欧拉角速率的转换矩阵(R2)

        将机体坐标系角速度[p,q,r]转换为欧拉角速率[phi_dot,theta_dot,psi_dot]

        参数:
            phi (float): 横滚角 (rad)
            theta (float): 俯仰角 (rad)
            psi (float): 偏航角 (rad) - 此参数在当前实现中未使用

        返回:
            numpy.ndarray: 3x3转换矩阵
        """
        cphi = np.cos(phi); sphi = np.sin(phi)
        ctheta = np.cos(theta); stheta = np.sin(theta)  # stheta在当前实现中未使用
        ttheta = np.tan(theta)
        R = np.array([
            [1, sphi*ttheta, cphi*ttheta],
            [0, cphi,       -sphi],
            [0, sphi/ctheta, cphi/ctheta]
        ])
        return R

    def immersion_ratio(self, z):
        """
        计算浸没比例H_hat，来自论文方程(3)

        其中z是地球坐标系的z坐标（向上为正），水面为z=0。
        在论文记号中，z是质心的z坐标。

        参数:
            z (float): 地球坐标系中的z位置 (m)

        返回:
            float: 浸没比例，0表示完全在空气中，1表示完全在水中
        """
        Hhat = (0.5*self.H - z) / self.H
        return Hhat

    def ks(self, Hhat):
        """
        线性损失系数ks，来自方程(6)

        参数:
            Hhat (float): 浸没比例

        返回:
            float: 损失系数，范围[0,1]
        """
        if Hhat < 0:
            return 0.0      # 完全在空气中
        elif Hhat > 1:
            return 1.0      # 完全在水中
        else:
            return float(Hhat)  # 过渡区域

    def KM_KJ(self, Hhat):
        """
        损失系数矩阵KM, KJ

        默认：对每个对角元素使用相同的标量ks
        （论文拟合了三次曲线；这里我们提供简单默认值并允许用户覆盖）

        参数:
            Hhat (float): 浸没比例

        返回:
            tuple: (KM, KJ) - 附加质量和附加惯性的损失系数矩阵
        """
        k = self.ks(Hhat)
        KM = np.diag([k,k,k])  # 附加质量损失系数矩阵
        KJ = np.diag([k,k,k])  # 附加惯性损失系数矩阵
        return KM, KJ

    def Ma_matrix(self, Hhat):
        """
        给定浸没比例Hhat的附加质量矩阵（应用KM）

        参数:
            Hhat (float): 浸没比例

        返回:
            numpy.ndarray: 3x3附加质量矩阵
        """
        KM, _ = self.KM_KJ(Hhat)
        return np.diag(self.Ma_diag) * KM  # 逐元素缩放

    def Ja_matrix(self, Hhat):
        """
        给定浸没比例Hhat的附加惯性矩阵（应用KJ）

        参数:
            Hhat (float): 浸没比例

        返回:
            numpy.ndarray: 3x3附加惯性矩阵
        """
        _, KJ = self.KM_KJ(Hhat)
        return np.diag(self.Ja_diag) * KJ

    def added_coupled(self, uvec, omega_vec, Hhat):
        """
        计算耦合附加力F_add和附加力矩M_add，基于方程(4)

        参数:
            uvec (numpy.ndarray): 机体线速度向量 [u,v,w] (m/s)
            omega_vec (numpy.ndarray): 机体角速度向量 [p,q,r] (rad/s)
            Hhat (float): 浸没比例 - 此参数在当前实现中未使用

        返回:
            tuple: (Fadd_matrix, Madd) - 附加力向量和附加力矩向量
        """
        # uvec = [u,v,w], omega_vec = [p,q,r]
        u, v, w = uvec
        p, q, r = omega_vec
        Xq = self.Xq_dot_force  # X方向对q角速度的耦合系数
        Yp = self.Yp_dot_force  # Y方向对p角速度的耦合系数

        # 更忠实于方程(4)的矩阵形式：
        mat1 = np.array([[0,0, Yp*p],
                         [0,0,-Xq*q],
                         [-Yp*p, Xq*q, 0]])
        Fadd_matrix = - mat1.dot(np.array([p,q,r]))

        # 论文中的Madd由两部分组成
        matA = np.array([[0,0, Yp*p],
                         [0,0,-Xq*q],
                         [-Yp*p, Xq*q, 0]])
        matB = np.array([[0,0, Xq*u],
                         [0,0,-Yp*v],
                         [-Xq*u, Yp*v, 0]])
        Madd = - matA.dot(np.array([u,v,w])) - matB.dot(np.array([p,q,r]))

        # 返回矩阵计算的Madd和Fadd（偏好更详细的版本）
        return Fadd_matrix, Madd

    def drag_force_moment(self, v_body, omega_body, Hhat):
        """
        阻力（非线性）力和力矩：- ks * D * v * |v|（逐元素）

        参数:
            v_body (numpy.ndarray): 机体坐标系线速度 [u,v,w] (m/s)
            omega_body (numpy.ndarray): 机体坐标系角速度 [p,q,r] (rad/s)
            Hhat (float): 浸没比例

        返回:
            tuple: (Fd, Md) - 阻力向量和阻力矩向量
        """
        ks = self.ks(Hhat)
        v = v_body
        # 逐元素：D对角矩阵乘以v * |v|
        Fd = - ks * (self.DM.dot(v * np.abs(v)))
        Md = - ks * (self.DJ.dot(omega_body * np.abs(omega_body)))
        return Fd, Md

    def restoring(self, phi, theta, psi, Hhat):
        """
        恢复力：g1和g2（使用方程(7)，为简化忽略rB贡献）

        参数:
            phi (float): 横滚角 (rad)
            theta (float): 俯仰角 (rad)
            psi (float): 偏航角 (rad)
            Hhat (float): 浸没比例

        返回:
            tuple: (g1, g2) - 重力恢复力向量和浮力恢复力矩向量
        """
        k_s = self.ks(Hhat)
        m_eff = self.m - k_s * self.rho_water * self.V  # 有效质量

        # 重力恢复力（机体坐标系）
        g1 = np.array([ - m_eff * self.g * np.sin(theta),
                        m_eff * self.g * np.sin(phi) * np.cos(theta),
                        m_eff * self.g * np.cos(phi) * np.cos(theta) ])

        # 为简化设置rB ~ 0；更精确的rB需要过渡期间的几何积分
        rB = np.zeros(3)

        # 地球坐标系中的浮力向量
        fB = np.array([0.0, 0.0, k_s * self.rho_water * self.g * self.V])

        # 使用R1^T将fB转换到机体坐标系（R1是机体->地球，所以R1^T是地球->机体）
        R = self.R1(phi, theta, psi)
        fB_body = R.T.dot(fB)
        g2 = np.cross(rB, fB_body)  # 目前为零但为完整性而包含
        return g1, g2

    def thrust_from_omega(self, omega_rpm, immersed):
        """
        螺旋桨推力模型：遵循论文的T = CT * omega^2（omega以rpm为单位）
        根据螺旋桨是在空气中还是水中选择CT（这里不显式使用rho）

        参数:
            omega_rpm (float): 螺旋桨转速 (rpm)
            immersed (bool): 如果螺旋桨在水中则为True，否则为False

        返回:
            float: 推力 (N)
        """
        # immersed: 布尔值，如果螺旋桨在水中则为True -> 使用CT_water，否则CT_air
        CT = self.CT_water if immersed else self.CT_air
        # 论文中使用的简单经验关系：T = CT * omega^2
        T = CT * (omega_rpm**2)
        return T

    def control_from_thrusts(self, Tvec):
        """
        使用方程(11)将四个螺旋桨推力映射到Fc和Mc

        参数:
            Tvec (numpy.ndarray): 四个螺旋桨推力 [T1,T2,T3,T4] (N)

        返回:
            tuple: (Fc, Mc) - 控制力向量和控制力矩向量
        """
        # Tvec是[T1,T2,T3,T4]
        T1,T2,T3,T4 = Tvec
        uf = T1+T2+T3+T4  # 总向上推力
        tau_p = 0 - self.L*T2 + 0 + self.L*T4   # 横滚力矩（第二行）
        tau_q = -self.L*T1 + 0 + self.L*T3 + 0  # 俯仰力矩（第三行）
        # 偏航力矩与符号模式*d成正比
        tau_r = self.d*T1 - self.d*T2 + self.d*T3 - self.d*T4
        Fc = np.array([0.0, 0.0, uf])  # 控制力（仅z方向）
        Mc = np.array([tau_p, tau_q, tau_r])  # 控制力矩
        return Fc, Mc

    def state_derivative(self, state, omegas_rpm, Fe_ext=None, Me_ext=None):
        """
        完整的右端项：给定状态和转子速度计算加速度

        这是HAUV动力学的核心函数，计算所有状态变量的时间导数。

        参数:
            state (dict): 状态字典，包含键：
                - 'pos' (3): 地球坐标系位置 [x,y,z] (m)
                - 'eul' (3): 欧拉角 [phi,theta,psi] (rad)
                - 'v' (3): 机体坐标系线速度 [u,v,w] (m/s)
                - 'omega' (3): 机体坐标系角速度 [p,q,r] (rad/s)
            omegas_rpm (numpy.ndarray): 4个转子速度 (rpm)
            Fe_ext (numpy.ndarray, optional): 外部扰动力 (3) (N)
            Me_ext (numpy.ndarray, optional): 外部扰动力矩 (3) (N·m)

        返回:
            dict: 包含所有状态导数和辅助信息的字典
        """
        pos = np.array(state['pos'])
        eul = np.array(state['eul'])
        v = np.array(state['v'])
        omega = np.array(state['omega'])
        x,y,z = pos; phi,theta,psi = eul  # x,y在当前实现中未使用

        # 浸没比例
        Hhat = self.immersion_ratio(z)

        # 矩阵计算
        KM, KJ = self.KM_KJ(Hhat)
        Ma = np.diag(self.Ma_diag)  # 基准附加质量 - 在当前实现中未使用
        Ja = np.diag(self.Ja_diag)  # 基准附加惯性 - 在当前实现中未使用
        M_eff = np.diag([self.m,self.m,self.m]) - KM.dot(np.diag(self.Ma_diag))  # 有效质量矩阵
        J_eff = self.I - KJ.dot(np.diag(self.Ja_diag))  # 有效惯性矩阵

        # 附加耦合项
        Fadd, Madd = self.added_coupled(v, omega, Hhat)

        # 阻力
        Fd, Md = self.drag_force_moment(v, omega, Hhat)

        # 恢复力
        g1, g2 = self.restoring(phi, theta, psi, Hhat)

        # 控制：估计哪些螺旋桨被浸没（简单高度测试：比较螺旋桨z与水面）
        # 计算地球坐标系中的螺旋桨坐标：来自论文的P_B矩阵
        PB = np.array([[ self.L/2, 0, -self.L/2, 0],      # 螺旋桨相对机体中心的x坐标
                       [ 0, -self.L/2, 0, self.L/2],       # 螺旋桨相对机体中心的y坐标
                       [ self.H/2, self.H/2, self.H/2, self.H/2 ]])  # 螺旋桨相对机体中心的z坐标
        R = self.R1(phi, theta, psi)
        POB = np.vstack([pos[0]*np.ones(4), pos[1]*np.ones(4), pos[2]*np.ones(4)])  # 机体中心位置
        PE = POB + R.dot(PB)  # 地球坐标系中的螺旋桨位置

        # 计算每个螺旋桨的推力
        Tvec = []
        for i in range(4):
            zpi = PE[2,i]  # 第i个螺旋桨的z坐标
            immersed = (self.immersion_ratio(zpi) > 0)  # 如果螺旋桨在水面以下则为True
            Tvec.append(self.thrust_from_omega(omegas_rpm[i], immersed))
        Tvec = np.array(Tvec)

        # 从推力计算控制力和力矩
        Fc, Mc = self.control_from_thrusts(Tvec)

        # 外部扰动
        if Fe_ext is None:
            Fe_ext = np.zeros(3)
        if Me_ext is None:
            Me_ext = np.zeros(3)

        # 构建科里奥利类项近似（使用叉积）
        M0 = np.diag([self.m,self.m,self.m])  # 基础质量矩阵
        J0 = self.I  # 基础惯性矩阵

        # 左端动力学方程（线性加速度）
        # (M0 - KM*Ma) * v_dot - [ (M0 - KM*Ma) v ] x omega + Fadd - ks * DM * v*|v| = Fc + Fe - g1
        lhs_mass = M0 - KM.dot(np.diag(self.Ma_diag))  # 左端质量矩阵
        coriolis_lin = np.cross(lhs_mass.dot(v), omega)  # 线性科里奥利项
        ks_val = self.ks(Hhat)

        # 通过重新排列计算v_dot（简单显式求解）
        rhs_lin = Fc + Fe_ext - g1 - Fadd + ks_val * (self.DM.dot(v * np.abs(v))) - coriolis_lin
        # 求解v_dot（反转lhs_mass）
        v_dot = np.linalg.solve(lhs_mass, rhs_lin)

        # 角度方程类似：
        lhs_J = J0 - KJ.dot(np.diag(self.Ja_diag))  # 左端惯性矩阵
        coriolis_ang = np.cross(lhs_J.dot(omega), omega)  # 角度科里奥利项
        # 额外项 (KMMa v) x v -> 近似 KMMa v = KM*Ma * v
        extra = np.cross(KM.dot(np.diag(self.Ma_diag)).dot(v), v)
        rhs_ang = Mc + Me_ext - g2 - Madd + ks_val * (self.DJ.dot(omega * np.abs(omega))) - coriolis_ang - extra
        omega_dot = np.linalg.solve(lhs_J, rhs_ang)

        # 运动学：pos_dot = R1 * v（机体->地球），eul_dot = R2 * omega
        pos_dot = R.dot(v)
        eul_dot = self.R2(phi, theta, psi).dot(omega)

        return {
            'pos_dot': pos_dot,    # 位置导数（地球坐标系）
            'eul_dot': eul_dot,    # 欧拉角导数
            'v_dot': v_dot,        # 线速度导数（机体坐标系）
            'omega_dot': omega_dot, # 角速度导数（机体坐标系）
            'Tvec': Tvec,          # 各螺旋桨推力
            'Hhat': Hhat           # 浸没比例
        }

# ----------------- 使用示例 ------------------
# 创建HAUV动力学实例
dyn = HAUVDynamics()

# 示例状态：在水下-1米附近悬停，零速度和小倾斜
state = {
    'pos': np.array([0.0, 0.0, -1.0]),   # x,y,z坐标 (z=-1 m: 水面下1米)
    'eul': np.array([0.01, 0.02, 0.0]),  # 小横滚/俯仰角 (弧度)
    'v': np.array([0.0, 0.0, 0.0]),      # 机体线速度 u,v,w
    'omega': np.array([0.0, 0.0, 0.0])   # 机体角速度 p,q,r
}

# 转子速度 (rpm) - 论文中使用的悬停示例约1890 rpm
omegas_rpm = np.array([1890.0, 1890.0, 1890.0, 1890.0])

# 计算状态导数
res = dyn.state_derivative(state, omegas_rpm)

# 输出结果
print("浸没比例 Hhat =", res['Hhat'])
print("各螺旋桨推力 (T1..T4) =", res['Tvec'])
print("位置导数 pos_dot (地球坐标系) =", res['pos_dot'])
print("线速度导数 v_dot (机体坐标系) =", res['v_dot'])
print("欧拉角导数 eul_dot =", res['eul_dot'])
print("角速度导数 omega_dot =", res['omega_dot'])

