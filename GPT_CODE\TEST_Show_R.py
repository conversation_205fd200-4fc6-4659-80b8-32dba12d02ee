# Python code to implement the Section 2 dynamics (compact, runnable).
# Notes:
# - Parameter values (mass, geometry, damping, added-mass, propeller CT/CQ, etc.)
#   are taken from Table 2 & Table 3 of the uploaded paper. See citation in the chat.
# - Some modeling choices are simplified (linear loss-coeff = H in transition,
#   rB set to zero except using a simple approximate during transition).
# - Units & propeller coefficient conventions in the paper are slightly nonstandard;
#   this code uses the *numeric* CT/CQ values from the paper and treats omega as RPM
#   in the thrust formula T = CT * omega_rpm**2 (this matches the table's form used by authors).
#   If you prefer SI-consistent T = rho * CT_dimless * (omega_rad_s**2) * D**4,
#   let me know and I will swap the formula + convert coefficients.

import numpy as np

class HAUVDynamics:
    def __init__(self):
        # --- Table 3 parameters (from the paper) ---
        self.m = 2.0                      # kg
        self.H = 0.3                      # m (body height)
        self.R = 0.04                     # m (body radius)
        self.L = 0.4                      # m (arm length)
        self.rho_air = 1.29               # kg/m^3
        self.rho_water = 1025.0           # kg/m^3
        self.I = np.diag([0.032,0.032,0.026])  # Ixx, <PERSON>y<PERSON>, Izz (kg*m^2)
        # hydrodynamic damping (nonlinear coefficients multiplied by v*|v|)
        self.DM = np.diag([-154.0, -154.0, -129.0])   # Xu, Yv, Zw - note signs as in table
        self.DJ = np.diag([-10.0, -10.0, -15.0])      # Kp, Mq, Nr (rotational damping)
        # added mass (Ma) and added inertia (Ja) baseline values (paper lists negative values)
        self.Ma_diag = np.array([-1.39, -1.39, -0.94])   # Xu_dot, Yv_dot, Zw_dot (kg)
        self.Ja_diag = np.array([-0.027, -0.027, -0.013])# Kp_dot, Mq_dot, Nr_dot (kg*m^2)
        # coupled added mass coupling terms (Xq_dot, Yp_dot)
        self.Xq_dot = -0.014
        self.Yp_dot = -0.014
        # coupled *force* coefficients (used in F_add and M_add)
        self.Xq_dot_force = self.Xq_dot
        self.Yp_dot_force = self.Yp_dot

        # --- Propeller parameters (Table 2) ---
        # The paper used a 15-inch propeller; D in table is 15 inch -> convert to m for clarity
        self.D_prop_inch = 15.0
        self.D = 15.0 * 0.0254   # meters (15 in)
        # Authors give CT and CQ values (we load them as numeric constants)
        # Note: units in the paper tables are tied to rpm usage; here we keep the numeric values
        self.CT_air = 1.653e-3      # numeric from Table 2 (paper)
        self.CQ_air = 2.225e-7
        self.CT_water = 1.742e-3
        self.CQ_water = 2.322e-7
        # Rotor limits (rpm)
        self.omega_max_air = 3600.0
        self.omega_max_water = 310.0
        # d is CT/CQ ratio parameter used in thrust distribution (paper uses 'd' = CT/CQ)
        # We'll define d using air CT/CQ (as used in their sim); user can change per-mode.
        # To avoid division-by-zero, use safe guard
        self.d = (self.CT_air / self.CQ_air) if self.CQ_air!=0 else 0.0

        # gravity & volume (cylinder)
        self.g = 9.81
        self.V = np.pi * (self.R**2) * self.H  # cylinder volume

    # Rotation matrix from body to earth (R1) using Z-Y-X (phi,theta,psi)
    def R1(self, phi, theta, psi):
        cphi = np.cos(phi); sphi = np.sin(phi)
        ctheta = np.cos(theta); stheta = np.sin(theta)
        cpsi = np.cos(psi); spsi = np.sin(psi)
        R = np.array([
            [ctheta*cpsi, sphi*stheta*cpsi - cphi*spsi, cphi*stheta*cpsi + sphi*spsi],
            [ctheta*spsi, sphi*stheta*spsi + cphi*cpsi, cphi*stheta*spsi - sphi*cpsi],
            [-stheta,     sphi*ctheta,                 cphi*ctheta]
        ])
        return R

    # Angular velocity->Euler rates transform R2
    def R2(self, phi, theta, psi):
        cphi = np.cos(phi); sphi = np.sin(phi)
        ctheta = np.cos(theta); stheta = np.sin(theta)
        ttheta = np.tan(theta)
        R = np.array([
            [1, sphi*ttheta, cphi*ttheta],
            [0, cphi,       -sphi],
            [0, sphi/ctheta, cphi/ctheta]
        ])
        return R

    # immersion ratio H_hat from paper eq (3), where z is earth-frame z (positive upward),
    # and the water surface is z=0. In their notation z is center-of-mass z.
    def immersion_ratio(self, z):
        Hhat = (0.5*self.H - z) / self.H
        return Hhat

    # linear ks from eq (6)
    def ks(self, Hhat):
        if Hhat < 0:
            return 0.0
        elif Hhat > 1:
            return 1.0
        else:
            return float(Hhat)

    # loss coefficients KM, KJ - default: use same scalar ks for each diagonal element.
    # (paper fitted cubic curves; here we provide a simple default and allow user override)
    def KM_KJ(self, Hhat):
        k = self.ks(Hhat)
        KM = np.diag([k,k,k])
        KJ = np.diag([k,k,k])
        return KM, KJ

    # Added mass and inertia matrices given immersion Hhat (apply KM,KJ)
    def Ma_matrix(self, Hhat):
        KM, _ = self.KM_KJ(Hhat)
        return np.diag(self.Ma_diag) * KM  # elementwise scaling

    def Ja_matrix(self, Hhat):
        _, KJ = self.KM_KJ(Hhat)
        return np.diag(self.Ja_diag) * KJ

    # Added (coupled) F_add and M_add per eq (4)
    def added_coupled(self, uvec, omega_vec, Hhat):
        # uvec = [u,v,w], omega_vec = [p,q,r]
        u, v, w = uvec
        p, q, r = omega_vec
        Xq = self.Xq_dot_force
        Yp = self.Yp_dot_force

        # More faithful to Eq.(4) (matrix form):
        mat1 = np.array([[0,0, Yp*p],
                         [0,0,-Xq*q],
                         [-Yp*p, Xq*q, 0]])
        Fadd_matrix = - mat1.dot(np.array([p,q,r]))
        # the paper also has an Madd composed of two parts
        matA = np.array([[0,0, Yp*p],
                         [0,0,-Xq*q],
                         [-Yp*p, Xq*q, 0]])
        matB = np.array([[0,0, Xq*u],
                         [0,0,-Yp*v],
                         [-Xq*u, Yp*v, 0]])
        Madd = - matA.dot(np.array([u,v,w])) - matB.dot(np.array([p,q,r]))
        # return the Madd and Fadd matrix-computed (prefer the more detailed one)
        return Fadd_matrix, Madd

    # Drag (nonlinear) force and moment: - ks * D * v * |v| (elementwise)
    def drag_force_moment(self, v_body, omega_body, Hhat):
        ks = self.ks(Hhat)
        v = v_body
        # elementwise: D diag times v * |v|
        Fd = - ks * (self.DM.dot(v * np.abs(v)))
        Md = - ks * (self.DJ.dot(omega_body * np.abs(omega_body)))
        return Fd, Md

    # Restoring forces: g1 and g2 (we use eq (7) and ignore rB contribution for simplicity)
    def restoring(self, phi, theta, psi, Hhat):
        k_s = self.ks(Hhat)
        m_eff = self.m - k_s * self.rho_water * self.V
        g1 = np.array([ - m_eff * self.g * np.sin(theta),
                        m_eff * self.g * np.sin(phi) * np.cos(theta),
                        m_eff * self.g * np.cos(phi) * np.cos(theta) ])
        # For simplicity set rB ~ 0; more accurate rB requires geometric integral in transition
        rB = np.zeros(3)
        # buoyant force vector in earth frame
        fB = np.array([0.0, 0.0, k_s * self.rho_water * self.g * self.V])
        # convert fB to body frame using R1^T (R1 is body->earth so R1^T is earth->body)
        R = self.R1(phi, theta, psi)
        fB_body = R.T.dot(fB)
        g2 = np.cross(rB, fB_body)  # currently zero but included for completeness
        return g1, g2

    # Propeller thrust model: we follow paper's T = CT * omega^2 (omega in rpm)
    # choose CT depending on whether prop in air or water (rho not explicitly used here)
    def thrust_from_omega(self, omega_rpm, immersed):
        # immersed: boolean True if prop in water -> use CT_water else CT_air
        CT = self.CT_water if immersed else self.CT_air
        # simple empirical relation used in paper: T = CT * omega^2
        T = CT * (omega_rpm**2)
        return T

    # Map four prop thrusts to Fc and Mc using eq (11)
    def control_from_thrusts(self, Tvec):
        # Tvec is [T1,T2,T3,T4]
        T1,T2,T3,T4 = Tvec
        uf = T1+T2+T3+T4
        tau_p = 0 - self.L*T2 + 0 + self.L*T4   # second row
        tau_q = -self.L*T1 + 0 + self.L*T3 + 0  # third row
        # yaw torque proportional to sign pattern * d
        tau_r = self.d*T1 - self.d*T2 + self.d*T3 - self.d*T4
        Fc = np.array([0.0, 0.0, uf])
        Mc = np.array([tau_p, tau_q, tau_r])
        return Fc, Mc

    # Full right-hand side: compute accelerations given state and rotor speeds
    # state: dict with keys: 'pos' (3), 'eul' (3), 'v' (3) body velocities, 'omega' (3) body rates
    # omegas_rpm: array of 4 rotor speeds in rpm
    # z is earth-frame z position (needed for immersion)
    # Fe_ext, Me_ext external disturbances (3) each
    def state_derivative(self, state, omegas_rpm, Fe_ext=None, Me_ext=None):
        pos = np.array(state['pos'])
        eul = np.array(state['eul'])
        v = np.array(state['v'])
        omega = np.array(state['omega'])
        x,y,z = pos; phi,theta,psi = eul
        # immersion ratio
        Hhat = self.immersion_ratio(z)
        # matrices
        KM, KJ = self.KM_KJ(Hhat)
        Ma = np.diag(self.Ma_diag)  # baseline
        Ja = np.diag(self.Ja_diag)
        M_eff = np.diag([self.m,self.m,self.m]) - KM.dot(np.diag(self.Ma_diag))
        J_eff = self.I - KJ.dot(np.diag(self.Ja_diag))

        # added couplings
        Fadd, Madd = self.added_coupled(v, omega, Hhat)

        # drag
        Fd, Md = self.drag_force_moment(v, omega, Hhat)

        # restoring
        g1, g2 = self.restoring(phi, theta, psi, Hhat)

        # control: estimate which propellers are immersed (simple height test: compare prop z to water)
        # compute prop coordinates in earth frame: P_B matrix from paper
        PB = np.array([[ self.L/2, 0, -self.L/2, 0],
                       [ 0, -self.L/2, 0, self.L/2],
                       [ self.H/2, self.H/2, self.H/2, self.H/2 ]])
        R = self.R1(phi, theta, psi)
        POB = np.vstack([pos[0]*np.ones(4), pos[1]*np.ones(4), pos[2]*np.ones(4)])
        PE = POB + R.dot(PB)  # propeller positions in earth frame
        Tvec = []
        for i in range(4):
            zpi = PE[2,i]
            immersed = (self.immersion_ratio(zpi) > 0)  # True if prop below water surface
            Tvec.append(self.thrust_from_omega(omegas_rpm[i], immersed))
        Tvec = np.array(Tvec)

        Fc, Mc = self.control_from_thrusts(Tvec)

        # external disturbances
        if Fe_ext is None:
            Fe_ext = np.zeros(3)
        if Me_ext is None:
            Me_ext = np.zeros(3)

        # build coriolis-like term approximations (use cross products)
        M0 = np.diag([self.m,self.m,self.m])
        J0 = self.I
        # left-hand dynamic equations (linear acceleration)
        # (M0 - KM*Ma) * v_dot - [ (M0 - KM*Ma) v ] x omega + Fadd - ks * DM * v*|v| = Fc + Fe - g1
        lhs_mass = M0 - KM.dot(np.diag(self.Ma_diag))
        coriolis_lin = np.cross(lhs_mass.dot(v), omega)
        ks_val = self.ks(Hhat)
        # compute v_dot by rearranging (simple explicit solve)
        rhs_lin = Fc + Fe_ext - g1 - Fadd + ks_val * (self.DM.dot(v * np.abs(v))) - coriolis_lin
        # solve for v_dot (invert lhs_mass)
        v_dot = np.linalg.solve(lhs_mass, rhs_lin)

        # angular equation similarly:
        lhs_J = J0 - KJ.dot(np.diag(self.Ja_diag))
        coriolis_ang = np.cross(lhs_J.dot(omega), omega)
        # extra term (KMMa v) x v -> approximate KMMa v = KM*Ma * v
        extra = np.cross(KM.dot(np.diag(self.Ma_diag)).dot(v), v)
        rhs_ang = Mc + Me_ext - g2 - Madd + ks_val * (self.DJ.dot(omega * np.abs(omega))) - coriolis_ang - extra
        omega_dot = np.linalg.solve(lhs_J, rhs_ang)

        # kinematics: pos_dot = R1 * v (body->earth), eul_dot = R2 * omega
        pos_dot = R.dot(v)
        eul_dot = self.R2(phi, theta, psi).dot(omega)

        return {
            'pos_dot': pos_dot,
            'eul_dot': eul_dot,
            'v_dot': v_dot,
            'omega_dot': omega_dot,
            'Tvec': Tvec,
            'Hhat': Hhat
        }

# ----------------- Example usage ------------------
dyn = HAUVDynamics()

# Example state: hovering near -1 m underwater, zero velocities and small tilt
state = {
    'pos': np.array([0.0, 0.0, -1.0]),   # x,y,z (z=-1 m: 1 m below surface)
    'eul': np.array([0.01, 0.02, 0.0]),  # small roll/pitch (rad)
    'v': np.array([0.0, 0.0, 0.0]),      # body linear velocities u,v,w
    'omega': np.array([0.0, 0.0, 0.0])   # body angular rates p,q,r
}
# rotor speeds (rpm) - example hover ~1890 rpm used in paper
omegas_rpm = np.array([1890.0, 1890.0, 1890.0, 1890.0])

res = dyn.state_derivative(state, omegas_rpm)

print("Immersion ratio Hhat =", res['Hhat'])
print("Individual prop thrusts (T1..T4) =", res['Tvec'])
print("pos_dot (earth) =", res['pos_dot'])
print("v_dot (body) =", res['v_dot'])
print("eul_dot =", res['eul_dot'])
print("omega_dot =", res['omega_dot'])

