"""
阻尼计算模块
简化版本：移除BaseNode架构，改为直接函数调用
"""


def calculate_damping(z_position, max_damping, floating_obj_height):
    """
    根据物体位置计算阻尼力的函数
    
    Args:
        z_position: 物体Z位置 (m)
        max_damping: 最大阻尼值
        floating_obj_height: 浮动物体高度 (m)
    
    Returns:
        dict: {
            'linear_damping': 线性阻尼,
            'angular_damping': 角阻尼
        }
    """
    # 计算半高度
    half_height = floating_obj_height / 2
    
    # 计算位移百分比
    displacement_percentage = -z_position / floating_obj_height + 0.5
    
    # 根据位置计算阻尼
    if z_position >= half_height:
        # 物体在水面上方
        damping = 0.01
    elif z_position < -half_height:
        # 物体完全浸没
        damping = max_damping
    elif -half_height <= z_position < half_height:
        # 物体部分浸没
        damping = max_damping * displacement_percentage
    else:
        damping = 0.0
    
    return {
        'linear_damping': damping,
        'angular_damping': damping
    }
