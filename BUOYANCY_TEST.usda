#usda 1.0
(
    customLayerData = {
        dictionary cameraSettings = {
            dictionary Front = {
                double3 position = (5, 0, 0)
                double radius = 5
            }
            dictionary Perspective = {
                double3 position = (10.05278007907071, 5.001473905677391, 8.284883300276231)
                double3 target = (0.4686597677141471, 0.5675741907052139, -0.2950512859323986)
            }
            dictionary Right = {
                double3 position = (0, -5, 0)
                double radius = 5
            }
            dictionary Top = {
                double3 position = (0, 0, 5)
                double radius = 5
            }
            string boundCamera = "/OmniverseKit_Persp"
        }
        dictionary omni_layer = {
            string authoring_layer = "./BUOYANCY_TEST.usd"
            dictionary muteness = {
            }
        }
        dictionary renderSettings = {
            float3 "rtx:debugView:pixelDebug:textColor" = (0, 1e18, 0)
            int "rtx:externalFrameCounter" = 279357
            float3 "rtx:fog:fogColor" = (0.75, 0.75, 0.75)
            float3 "rtx:post:backgroundZeroAlpha:backgroundDefaultColor" = (0, 0, 0)
            float3 "rtx:post:colorcorr:contrast" = (1, 1, 1)
            float3 "rtx:post:colorcorr:gain" = (1, 1, 1)
            float3 "rtx:post:colorcorr:gamma" = (1, 1, 1)
            float3 "rtx:post:colorcorr:offset" = (0, 0, 0)
            float3 "rtx:post:colorcorr:saturation" = (1, 1, 1)
            float3 "rtx:post:colorgrad:blackpoint" = (0, 0, 0)
            float3 "rtx:post:colorgrad:contrast" = (1, 1, 1)
            float3 "rtx:post:colorgrad:gain" = (1, 1, 1)
            float3 "rtx:post:colorgrad:gamma" = (1, 1, 1)
            float3 "rtx:post:colorgrad:lift" = (0, 0, 0)
            float3 "rtx:post:colorgrad:multiply" = (1, 1, 1)
            float3 "rtx:post:colorgrad:offset" = (0, 0, 0)
            float3 "rtx:post:colorgrad:whitepoint" = (1, 1, 1)
            int "rtx:post:dlss:execMode" = 0
            float3 "rtx:post:lensDistortion:lensFocalLengthArray" = (10, 30, 50)
            float3 "rtx:post:lensFlares:anisoFlareFalloffX" = (450, 475, 500)
            float3 "rtx:post:lensFlares:anisoFlareFalloffY" = (10, 10, 10)
            float3 "rtx:post:lensFlares:cutoffPoint" = (2, 2, 2)
            float3 "rtx:post:lensFlares:haloFlareFalloff" = (10, 10, 10)
            float3 "rtx:post:lensFlares:haloFlareRadius" = (75, 75, 75)
            float3 "rtx:post:lensFlares:isotropicFlareFalloff" = (50, 50, 50)
            float3 "rtx:post:tonemap:whitepoint" = (1, 1, 1)
            float3 "rtx:raytracing:inscattering:singleScatteringAlbedo" = (0.9, 0.9, 0.9)
            float3 "rtx:raytracing:inscattering:transmittanceColor" = (0.5, 0.5, 0.5)
            float3 "rtx:sceneDb:ambientLightColor" = (0.1, 0.1, 0.1)
        }
    }
    defaultPrim = "World"
    endTimeCode = 100
    metersPerUnit = 1
    startTimeCode = 0
    timeCodesPerSecond = 24
    upAxis = "Z"
)

over "Render" (
    hide_in_stage_window = true
)
{
}

def Xform "World"
{
    def Mesh "Cube" (
        prepend apiSchemas = ["PhysicsRigidBodyAPI", "PhysxRigidBodyAPI", "PhysicsCollisionAPI", "PhysxCollisionAPI", "PhysxConvexHullCollisionAPI", "PhysicsMeshCollisionAPI", "PhysxForceAPI", "PhysicsMassAPI"]
    )
    {
        float3[] extent = [(-0.5, -0.5, -0.5), (0.5, 0.5, 0.5)]
        int[] faceVertexCounts = [4, 4, 4, 4, 4, 4]
        int[] faceVertexIndices = [0, 1, 3, 2, 4, 6, 7, 5, 4, 5, 1, 0, 6, 2, 3, 7, 4, 0, 2, 6, 5, 7, 3, 1]
        rel material:binding = </World/Looks/Plastic_ABS> (
            bindMaterialAs = "weakerThanDescendants"
        )
        rel material:binding:physics = None (
            bindMaterialAs = "weakerThanDescendants"
        )
        normal3f[] normals = [(0, 0, 1), (0, 0, 1), (0, 0, 1), (0, 0, 1), (0, 0, -1), (0, 0, -1), (0, 0, -1), (0, 0, -1), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, -1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (0, 1, 0), (-1, 0, 0), (-1, 0, 0), (-1, 0, 0), (-1, 0, 0), (1, 0, 0), (1, 0, 0), (1, 0, 0), (1, 0, 0)] (
            interpolation = "faceVarying"
        )
        vector3f physics:angularVelocity = (0, 0, 0)
        uniform token physics:approximation = "convexHull"
        bool physics:collisionEnabled = 1
        float physics:density = 1.234
        bool physics:kinematicEnabled = 0
        float physics:mass = 0.4
        bool physics:rigidBodyEnabled = 1
        vector3f physics:velocity = (-0.3626026, 0, -5.586976e-7)
        vector3f physxForce:force = (-0, -0, 3.9182372)
        uniform token physxForce:mode = "force"
        vector3f physxForce:torque = (0, 0, 0)
        float physxRigidBody:angularDamping = 0.79964024
        bool physxRigidBody:disableGravity = 0
        float physxRigidBody:linearDamping = 0.79964024
        int physxRigidBody:lockedPosAxis = 0
        point3f[] points = [(-0.5, -0.5, 0.5), (0.5, -0.5, 0.5), (-0.5, 0.5, 0.5), (0.5, 0.5, 0.5), (-0.5, -0.5, -0.5), (0.5, -0.5, -0.5), (-0.5, 0.5, -0.5), (0.5, 0.5, -0.5)]
        bool primvars:doNotCastShadows = 0
        float2[] primvars:st = [(0, 0), (1, 0), (1, 1), (0, 1), (1, 0), (0, 0), (0, 1), (1, 1), (0, 0), (0, 1), (1, 1), (1, 0), (0, 0), (0, 1), (1, 1), (1, 0), (0, 0), (1, 0), (1, 1), (0, 1), (1, 0), (0, 0), (0, 1), (1, 1)] (
            interpolation = "faceVarying"
        )
        uniform token subdivisionScheme = "none"
        quatd xformOp:orient = (1, 0, 0, 0)
        double3 xformOp:scale = (1, 1, 1)
        double3 xformOp:translate = (-40.95973587036133, -0.4087611734867096, 0.10017988830804825)
        uniform token[] xformOpOrder = ["xformOp:translate", "xformOp:orient", "xformOp:scale"]

        def OmniGraph "buoyancy"
        {
            token evaluationMode = "Automatic"
            token evaluator:type = "execution"
            token fabricCacheBacking = "Shared"
            int2 fileFormatVersion = (1, 9)
            token pipelineStage = "pipelineStageSimulation"

            def OmniGraphNode "CubeReadAttrib" (
                prepend apiSchemas = ["NodeGraphNodeAPI"]
            )
            {
                custom bool inputs:forceUSDRead = 0
                custom token inputs:name = "xformOp:translate"
                custom rel inputs:prim = </World/Cube> (
                    customData = {
                        dictionary omni = {
                            dictionary graph = {
                                string relType = "bundle"
                            }
                        }
                    }
                )
                custom token inputs:primPath
                custom timecode inputs:usdTimecode = nan
                custom bool inputs:usePath = 0
                token node:type = "omni.graph.nodes.ReadPrimAttribute"
                int node:typeVersion = 3
                custom token outputs:value
                custom bool state:correctlySetup
                custom uint64 state:importPath
                custom bool state:reimportAtTime
                custom uint64 state:srcAttrib
                custom uint64 state:srcPath
                custom uint64 state:srcPathAsToken
                custom double state:time
                uniform token ui:nodegraph:node:expansionState = "open"
                uniform float2 ui:nodegraph:node:pos = (-385.49313, 58.99006)
            }

            def OmniGraphNode "CubeWriteAttrib" (
                prepend apiSchemas = ["NodeGraphNodeAPI"]
            )
            {
                custom uint inputs:execIn
                prepend uint inputs:execIn.connect = </World/Cube/buoyancy/script_node.outputs:execOut>
                custom token inputs:layerIdentifier
                custom token inputs:name = "physxForce:force"
                custom rel inputs:prim = </World/Cube> (
                    customData = {
                        dictionary omni = {
                            dictionary graph = {
                                string relType = "bundle"
                            }
                        }
                    }
                )
                custom token inputs:primPath
                custom bool inputs:usdWriteBack = 1
                custom bool inputs:usePath = 0
                custom token inputs:value
                prepend token inputs:value.connect = </World/Cube/buoyancy/make_3_vector.outputs:tuple>
                token node:type = "omni.graph.nodes.WritePrimAttribute"
                int node:typeVersion = 3
                custom uint outputs:execOut (
                    customData = {
                        bool isExecution = 1
                    }
                )
                custom bool state:correctlySetup
                custom uint64 state:destAttrib
                custom uint64 state:destPath
                custom uint64 state:destPathToken
                custom token state:layerIdentifier
                custom token state:resolvedLayerIdentifier
                uniform token ui:nodegraph:node:expansionState = "open"
                uniform float2 ui:nodegraph:node:pos = (1182.3564, 116.11557)
            }

            def OmniGraphNode "on_playback_tick" (
                prepend apiSchemas = ["NodeGraphNodeAPI"]
            )
            {
                token node:type = "omni.graph.action.OnPlaybackTick"
                int node:typeVersion = 2
                custom double outputs:deltaSeconds
                custom double outputs:frame
                custom uint outputs:tick (
                    customData = {
                        bool isExecution = 1
                    }
                )
                custom double outputs:time
                uniform token ui:nodegraph:node:expansionState = "open"
                uniform float2 ui:nodegraph:node:pos = (-109.50984, -9.973383)
            }

            def OmniGraphNode "break_3_vector" (
                prepend apiSchemas = ["NodeGraphNodeAPI"]
            )
            {
                custom token inputs:tuple
                prepend token inputs:tuple.connect = </World/Cube/buoyancy/CubeReadAttrib.outputs:value>
                token node:type = "omni.graph.nodes.BreakVector3"
                int node:typeVersion = 1
                custom token outputs:x
                custom token outputs:y
                custom token outputs:z
                uniform token ui:nodegraph:node:expansionState = "open"
                uniform float2 ui:nodegraph:node:pos = (-113.39336, 157.71547)
            }

            def OmniGraphNode "make_3_vector" (
                prepend apiSchemas = ["NodeGraphNodeAPI"]
            )
            {
                custom token inputs:x
                prepend token inputs:x.connect = </World/Cube/buoyancy/script_node.outputs:x_force>
                custom token inputs:y
                prepend token inputs:y.connect = </World/Cube/buoyancy/script_node.outputs:y_force>
                custom token inputs:z
                prepend token inputs:z.connect = </World/Cube/buoyancy/script_node.outputs:z_force>
                token node:type = "omni.graph.nodes.MakeVector3"
                int node:typeVersion = 1
                custom token outputs:tuple
                uniform token ui:nodegraph:node:expansionState = "open"
                uniform float2 ui:nodegraph:node:pos = (933.75653, 329.24374)
            }

            def OmniGraphNode "script_node" (
                prepend apiSchemas = ["NodeGraphNodeAPI"]
            )
            {
                custom uint inputs:execIn
                prepend uint inputs:execIn.connect = </World/Cube/buoyancy/on_playback_tick.outputs:tick>
                custom float inputs:height
                prepend float inputs:height.connect = </World/Cube/buoyancy/constant_float_01.inputs:value>
                custom float3 inputs:rotation
                prepend float3 inputs:rotation.connect = </World/Cube/buoyancy/script_node_01.outputs:rotation>
                custom string inputs:script = """# This script is executed the first time the script node computes, or the next time it computes after this script
# is modified or the 'Reset' button is pressed.

# The following callback functions may be defined in this script:
#     setup(db): Called immediately after this script is executed
#     compute(db): Called every time the node computes (should always be defined)
#     cleanup(db): Called when the node is deleted or the reset button is pressed (if setup(db) was called before)

# Defining setup(db) and cleanup(db) is optional, but if compute(db) is not defined then this script node will run
# in legacy mode, where the entire script is executed on every compute and the callback functions above are ignored.

# Available variables:
#    db: og.Database The node interface, attributes are db.inputs.data, db.outputs.data.
#                    Use db.log_error, db.log_warning to report problems.
#                    Note that this is available outside of the callbacks only to support legacy mode.
#    og: The OmniGraph module

# Import statements, function/class definitions, and global variables may be placed outside of the callbacks.
# Variables may also be added to the db.internal_state state object.

# Example code snippet:

import math

UNITS = \"cm\"


def calculate_circumfrence(radius):
    return 2 * math.pi * radius


def setup(db):
    state = db.internal_state
    state.radius = 1


def compute(db):
    state = db.internal_state
    circumfrence = calculate_circumfrence(state.radius)
    print(f\"{circumfrence} {UNITS}\")
    state.radius += 1


# To see more examples, click on the Code Snippets button below."""
                custom token inputs:scriptPath = "scripts/buoyancy_forces.py"
                custom bool inputs:usePath = 1
                custom float inputs:volume
                prepend float inputs:volume.connect = </World/Cube/buoyancy/constant_float.inputs:value>
                custom float inputs:z_position
                prepend float inputs:z_position.connect = </World/Cube/buoyancy/break_3_vector.outputs:z>
                token node:type = "omni.graph.scriptnode.ScriptNode"
                int node:typeVersion = 1
                custom uint outputs:execOut (
                    customData = {
                        bool isExecution = 1
                    }
                )
                custom float outputs:x_force
                custom float outputs:y_force
                custom float outputs:z_force
                custom bool state:omni_initialized
                uniform token ui:nodegraph:node:expansionState = "open"
                uniform float2 ui:nodegraph:node:pos = (656.0068, 149.72527)
            }

            def OmniGraphNode "constant_float" (
                prepend apiSchemas = ["NodeGraphNodeAPI"]
            )
            {
                custom float inputs:value = 1
                token node:type = "omni.graph.nodes.ConstantFloat"
                int node:typeVersion = 1
                uniform token ui:nodegraph:node:expansionState = "open"
                uniform float2 ui:nodegraph:node:pos = (-118.89626, 337.5783)
            }

            def OmniGraphNode "CubeReadAttrib_01" (
                prepend apiSchemas = ["NodeGraphNodeAPI"]
            )
            {
                custom bool inputs:forceUSDRead = 0
                custom token inputs:name = "xformOp:orient"
                custom rel inputs:prim = </World/Cube> (
                    customData = {
                        dictionary omni = {
                            dictionary graph = {
                                string relType = "bundle"
                            }
                        }
                    }
                )
                custom token inputs:primPath
                custom timecode inputs:usdTimecode = nan
                custom bool inputs:usePath = 0
                token node:type = "omni.graph.nodes.ReadPrimAttribute"
                int node:typeVersion = 3
                custom token outputs:value
                custom bool state:correctlySetup
                custom uint64 state:importPath
                custom bool state:reimportAtTime
                custom uint64 state:srcAttrib
                custom uint64 state:srcPath
                custom uint64 state:srcPathAsToken
                custom double state:time
                uniform token ui:nodegraph:node:expansionState = "open"
                uniform float2 ui:nodegraph:node:pos = (-133.52306, 583.64746)
            }

            def OmniGraphNode "script_node_01" (
                prepend apiSchemas = ["NodeGraphNodeAPI"]
            )
            {
                custom uint inputs:execIn
                prepend uint inputs:execIn.connect = </World/Cube/buoyancy/on_playback_tick.outputs:tick>
                custom float4 inputs:quaternion
                prepend float4 inputs:quaternion.connect = </World/Cube/buoyancy/CubeReadAttrib_01.outputs:value>
                custom string inputs:script = """# This script is executed the first time the script node computes, or the next time it computes after this script
# is modified or the 'Reset' button is pressed.

# The following callback functions may be defined in this script:
#     setup(db): Called immediately after this script is executed
#     compute(db): Called every time the node computes (should always be defined)
#     cleanup(db): Called when the node is deleted or the reset button is pressed (if setup(db) was called before)

# Defining setup(db) and cleanup(db) is optional, but if compute(db) is not defined then this script node will run
# in legacy mode, where the entire script is executed on every compute and the callback functions above are ignored.

# Available variables:
#    db: og.Database The node interface, attributes are db.inputs.data, db.outputs.data.
#                    Use db.log_error, db.log_warning to report problems.
#                    Note that this is available outside of the callbacks only to support legacy mode.
#    og: The OmniGraph module

# Import statements, function/class definitions, and global variables may be placed outside of the callbacks.
# Variables may also be added to the db.internal_state state object.

# Example code snippet:

import math

UNITS = \"cm\"


def calculate_circumfrence(radius):
    return 2 * math.pi * radius


def setup(db):
    state = db.internal_state
    state.radius = 1


def compute(db):
    state = db.internal_state
    circumfrence = calculate_circumfrence(state.radius)
    print(f\"{circumfrence} {UNITS}\")
    state.radius += 1


# To see more examples, click on the Code Snippets button below."""
                custom token inputs:scriptPath = "scripts/quat_to_euler.py"
                custom bool inputs:usePath = 1
                token node:type = "omni.graph.scriptnode.ScriptNode"
                int node:typeVersion = 1
                custom uint outputs:execOut (
                    customData = {
                        bool isExecution = 1
                    }
                )
                custom float3 outputs:rotation
                custom bool state:omni_initialized
                uniform token ui:nodegraph:node:expansionState = "open"
                uniform float2 ui:nodegraph:node:pos = (301.07327, 409.9701)
            }

            def OmniGraphNode "constant_float_01" (
                prepend apiSchemas = ["NodeGraphNodeAPI"]
            )
            {
                custom float inputs:value = 1
                token node:type = "omni.graph.nodes.ConstantFloat"
                int node:typeVersion = 1
                uniform token ui:nodegraph:node:expansionState = "open"
                uniform float2 ui:nodegraph:node:pos = (-129.66034, 441.82938)
            }
        }

        def Material "Cube_material" (
            prepend apiSchemas = ["PhysicsMaterialAPI"]
        )
        {
            float physics:density = 1.2923
        }

        def OmniGraph "damping"
        {
            token evaluationMode = "Automatic"
            token evaluator:type = "execution"
            token fabricCacheBacking = "Shared"
            int2 fileFormatVersion = (1, 9)
            token pipelineStage = "pipelineStageSimulation"

            def OmniGraphNode "CubeReadAttrib" (
                prepend apiSchemas = ["NodeGraphNodeAPI"]
            )
            {
                custom bool inputs:forceUSDRead = 0
                custom token inputs:name = "xformOp:translate"
                custom rel inputs:prim = </World/Cube> (
                    customData = {
                        dictionary omni = {
                            dictionary graph = {
                                string relType = "bundle"
                            }
                        }
                    }
                )
                custom token inputs:primPath
                custom timecode inputs:usdTimecode = nan
                custom bool inputs:usePath = 0
                token node:type = "omni.graph.nodes.ReadPrimAttribute"
                int node:typeVersion = 3
                custom token outputs:value
                custom bool state:correctlySetup
                custom uint64 state:importPath
                custom bool state:reimportAtTime
                custom uint64 state:srcAttrib
                custom uint64 state:srcPath
                custom uint64 state:srcPathAsToken
                custom double state:time
                uniform token ui:nodegraph:node:expansionState = "open"
                uniform float2 ui:nodegraph:node:pos = (100.5683, 138.02452)
            }

            def OmniGraphNode "CubeWriteAttrib" (
                prepend apiSchemas = ["NodeGraphNodeAPI"]
            )
            {
                custom uint inputs:execIn
                prepend uint inputs:execIn.connect = </World/Cube/damping/script_node.outputs:execOut>
                custom token inputs:layerIdentifier
                custom token inputs:name = "physxRigidBody:linearDamping"
                custom rel inputs:prim = </World/Cube> (
                    customData = {
                        dictionary omni = {
                            dictionary graph = {
                                string relType = "bundle"
                            }
                        }
                    }
                )
                custom token inputs:primPath
                custom bool inputs:usdWriteBack = 1
                custom bool inputs:usePath = 0
                custom token inputs:value
                prepend token inputs:value.connect = </World/Cube/damping/script_node.outputs:linear_damping>
                token node:type = "omni.graph.nodes.WritePrimAttribute"
                int node:typeVersion = 3
                custom uint outputs:execOut (
                    customData = {
                        bool isExecution = 1
                    }
                )
                custom bool state:correctlySetup
                custom uint64 state:destAttrib
                custom uint64 state:destPath
                custom uint64 state:destPathToken
                custom token state:layerIdentifier
                custom token state:resolvedLayerIdentifier
                uniform token ui:nodegraph:node:expansionState = "open"
                uniform float2 ui:nodegraph:node:pos = (1019.6813, 96.350525)
            }

            def OmniGraphNode "on_playback_tick" (
                prepend apiSchemas = ["NodeGraphNodeAPI"]
            )
            {
                token node:type = "omni.graph.action.OnPlaybackTick"
                int node:typeVersion = 2
                custom double outputs:deltaSeconds
                custom double outputs:frame
                custom uint outputs:tick (
                    customData = {
                        bool isExecution = 1
                    }
                )
                custom double outputs:time
                uniform token ui:nodegraph:node:expansionState = "open"
                uniform float2 ui:nodegraph:node:pos = (392.7041, -26.211546)
            }

            def OmniGraphNode "break_3_vector" (
                prepend apiSchemas = ["NodeGraphNodeAPI"]
            )
            {
                custom token inputs:tuple
                prepend token inputs:tuple.connect = </World/Cube/damping/CubeReadAttrib.outputs:value>
                token node:type = "omni.graph.nodes.BreakVector3"
                int node:typeVersion = 1
                custom token outputs:x
                custom token outputs:y
                custom token outputs:z
                uniform token ui:nodegraph:node:expansionState = "open"
                uniform float2 ui:nodegraph:node:pos = (387.72754, 162.84038)
            }

            def OmniGraphNode "script_node" (
                prepend apiSchemas = ["NodeGraphNodeAPI"]
            )
            {
                custom uint inputs:execIn
                prepend uint inputs:execIn.connect = </World/Cube/damping/on_playback_tick.outputs:tick>
                custom float inputs:floating_obj_height
                prepend float inputs:floating_obj_height.connect = </World/Cube/damping/constant_float.inputs:value>
                custom float inputs:max_damping
                prepend float inputs:max_damping.connect = </World/Cube/damping/constant_float_01.inputs:value>
                custom string inputs:script = """def setup(db):
    pass

def compute(db):
    z_pos = db.inputs.z_position
    z_vel = db.inputs.velocity
    
    if z_pos >= 0.5:
       dumping = 0.1
    elif z_pos < -0.5:
       dumping = 2.0
    elif z_pos > -0.5 and z_pos < 0.5 and z_vel > 0:
       dumping = 0.0
    elif z_pos > -0.5 and z_pos < 0.5 and z_vel < 0:
       dumping = 2.0
    
    db.outputs.damping = dumping
    """
                custom token inputs:scriptPath = "./scripts/damping.py"
                custom bool inputs:usePath = 1
                custom float inputs:z_position
                prepend float inputs:z_position.connect = </World/Cube/damping/break_3_vector.outputs:z>
                token node:type = "omni.graph.scriptnode.ScriptNode"
                int node:typeVersion = 1
                custom float outputs:angular_damping
                custom uint outputs:execOut (
                    customData = {
                        bool isExecution = 1
                    }
                )
                custom float outputs:linear_damping
                custom bool state:omni_initialized
                uniform token ui:nodegraph:node:expansionState = "open"
                uniform float2 ui:nodegraph:node:pos = (684.27295, 162.84558)

                def AnimationData "animationData"
                {
                    uniform int64[] outputs:damping:x:inTangentTimes = [0]
                    uniform token[] outputs:damping:x:inTangentTypes = ["auto"]
                    uniform double[] outputs:damping:x:inTangentValues = [0]
                    uniform int64[] outputs:damping:x:outTangentTimes = [0]
                    uniform token[] outputs:damping:x:outTangentTypes = ["auto"]
                    uniform double[] outputs:damping:x:outTangentValues = [0]
                    uniform bool[] outputs:damping:x:tangentBrokens = [0]
                    uniform bool[] outputs:damping:x:tangentWeighteds = [0]
                    uniform int64[] outputs:damping:x:times = [0]
                    uniform double[] outputs:damping:x:values = [0]
                }
            }

            def OmniGraphNode "write_prim_attribute" (
                prepend apiSchemas = ["NodeGraphNodeAPI"]
            )
            {
                custom uint inputs:execIn
                prepend uint inputs:execIn.connect = </World/Cube/damping/script_node.outputs:execOut>
                custom token inputs:layerIdentifier
                custom token inputs:name = "physxRigidBody:angularDamping"
                custom rel inputs:prim = </World/Cube> (
                    customData = {
                        dictionary omni = {
                            dictionary graph = {
                                string relType = "bundle"
                            }
                        }
                    }
                )
                custom token inputs:primPath
                custom bool inputs:usdWriteBack = 1
                custom bool inputs:usePath = 0
                custom token inputs:value
                prepend token inputs:value.connect = </World/Cube/damping/script_node.outputs:angular_damping>
                token node:type = "omni.graph.nodes.WritePrimAttribute"
                int node:typeVersion = 3
                custom uint outputs:execOut (
                    customData = {
                        bool isExecution = 1
                    }
                )
                custom bool state:correctlySetup
                custom uint64 state:destAttrib
                custom uint64 state:destPath
                custom uint64 state:destPathToken
                custom token state:layerIdentifier
                custom token state:resolvedLayerIdentifier
                uniform token ui:nodegraph:node:expansionState = "open"
                uniform float2 ui:nodegraph:node:pos = (1022.73627, 340.33157)
            }

            def OmniGraphNode "constant_float" (
                prepend apiSchemas = ["NodeGraphNodeAPI"]
            )
            {
                custom float inputs:value = 1
                token node:type = "omni.graph.nodes.ConstantFloat"
                int node:typeVersion = 1
                uniform token ui:nodegraph:node:expansionState = "open"
                uniform float2 ui:nodegraph:node:pos = (377.73474, 319.43045)
            }

            def OmniGraphNode "constant_float_01" (
                prepend apiSchemas = ["NodeGraphNodeAPI"]
            )
            {
                custom float inputs:value = 2
                token node:type = "omni.graph.nodes.ConstantFloat"
                int node:typeVersion = 1
                uniform token ui:nodegraph:node:expansionState = "open"
                uniform float2 ui:nodegraph:node:pos = (379.8319, 433.5215)
            }
        }
    }

    def Scope "Looks"
    {
        def Material "Water_Opaque"
        {
            token outputs:mdl:displacement.connect = </World/Looks/Water_Opaque/Shader.outputs:out>
            token outputs:mdl:surface.connect = </World/Looks/Water_Opaque/Shader.outputs:out>
            token outputs:mdl:volume.connect = </World/Looks/Water_Opaque/Shader.outputs:out>

            def Shader "Shader"
            {
                uniform token info:implementationSource = "sourceAsset"
                uniform asset info:mdl:sourceAsset = @http://omniverse-content-production.s3-us-west-2.amazonaws.com/Materials/Base/Natural/Water_Opaque.mdl@
                uniform token info:mdl:sourceAsset:subIdentifier = "Water_Opaque"
                token outputs:out (
                    renderType = "material"
                )
            }
        }

        def Material "Water"
        {
            token outputs:mdl:displacement.connect = </World/Looks/Water/Shader.outputs:out>
            token outputs:mdl:surface.connect = </World/Looks/Water/Shader.outputs:out>
            token outputs:mdl:volume.connect = </World/Looks/Water/Shader.outputs:out>

            def Shader "Shader"
            {
                uniform token info:implementationSource = "sourceAsset"
                uniform asset info:mdl:sourceAsset = @http://omniverse-content-production.s3-us-west-2.amazonaws.com/Materials/Base/Natural/Water.mdl@
                uniform token info:mdl:sourceAsset:subIdentifier = "Water"
                token outputs:out (
                    renderType = "material"
                )
            }
        }

        def Material "Water_Opaque_01"
        {
            token outputs:mdl:displacement.connect = </World/Looks/Water_Opaque_01/Shader.outputs:out>
            token outputs:mdl:surface.connect = </World/Looks/Water_Opaque_01/Shader.outputs:out>
            token outputs:mdl:volume.connect = </World/Looks/Water_Opaque_01/Shader.outputs:out>

            def Shader "Shader"
            {
                uniform token info:implementationSource = "sourceAsset"
                uniform asset info:mdl:sourceAsset = @http://omniverse-content-production.s3-us-west-2.amazonaws.com/Materials/Base/Natural/Water_Opaque.mdl@
                uniform token info:mdl:sourceAsset:subIdentifier = "Water_Opaque"
                float inputs:texture_rotate = 9092.41 (
                    customData = {
                        float default = 0
                    }
                    displayGroup = "UV"
                    displayName = "Texture Rotate"
                    doc = "Rotates angle of texture in degrees."
                    hidden = false
                )
                token outputs:out (
                    renderType = "material"
                )
            }
        }

        def Material "Plastic_ABS"
        {
            token outputs:mdl:displacement.connect = </World/Looks/Plastic_ABS/Shader.outputs:out>
            token outputs:mdl:surface.connect = </World/Looks/Plastic_ABS/Shader.outputs:out>
            token outputs:mdl:volume.connect = </World/Looks/Plastic_ABS/Shader.outputs:out>

            def Shader "Shader"
            {
                uniform token info:implementationSource = "sourceAsset"
                uniform asset info:mdl:sourceAsset = @http://omniverse-content-production.s3-us-west-2.amazonaws.com/Materials/Base/Plastics/Plastic_ABS.mdl@
                uniform token info:mdl:sourceAsset:subIdentifier = "Plastic_ABS"
                color3f inputs:diffuse_tint = (0.17721534, 1, 0) (
                    customData = {
                        float3 default = (0.005, 0.005, 0.005)
                    }
                    displayGroup = "Albedo"
                    displayName = "Color Tint"
                    doc = "When enabled, this color value is multiplied over the final albedo color"
                    hidden = false
                )
                color3f inputs:emissive_color = (0.3797469, 1, 0) (
                    customData = {
                        float3 default = (0, 0, 0)
                    }
                    displayGroup = "Emissive"
                    displayName = "Emissive Color"
                    doc = "The emission color"
                    hidden = false
                )
                token outputs:out (
                    renderType = "material"
                )
            }
        }
    }

    def PhysicsScene "PhysicsScene"
    {
        vector3f physics:gravityDirection = (-0.03, 0, -1)
        float physics:gravityMagnitude = 9.8
    }

    def Mesh "water"
    {
        float3[] extent = [(-0.5, -0.5, 0), (0.5, 0.5, 0)]
        int[] faceVertexCounts = [4]
        int[] faceVertexIndices = [0, 1, 3, 2]
        rel material:binding = </World/Looks/Water_Opaque_01> (
            bindMaterialAs = "weakerThanDescendants"
        )
        normal3f[] normals = [(0, 0, 1), (0, 0, 1), (0, 0, 1), (0, 0, 1)] (
            interpolation = "faceVarying"
        )
        point3f[] points = [(-0.5, -0.5, 0), (0.5, -0.5, 0), (-0.5, 0.5, 0), (0.5, 0.5, 0)]
        bool primvars:doNotCastShadows = 0
        float2[] primvars:st = [(0, 0), (1, 0), (1, 1), (0, 1)] (
            interpolation = "faceVarying"
        )
        uniform token subdivisionScheme = "none"
        quatd xformOp:orient = (1, 0, 0, 0)
        double3 xformOp:scale = (100, 100, 1)
        double3 xformOp:translate = (0, 0, 0)
        uniform token[] xformOpOrder = ["xformOp:translate", "xformOp:orient", "xformOp:scale"]

        def OmniGraph "animate_water"
        {
            token evaluationMode = "Automatic"
            token evaluator:type = "execution"
            token fabricCacheBacking = "Shared"
            int2 fileFormatVersion = (1, 9)
            token pipelineStage = "pipelineStageSimulation"

            def OmniGraphNode "ShaderWriteAttrib" (
                prepend apiSchemas = ["NodeGraphNodeAPI"]
            )
            {
                custom uint inputs:execIn
                prepend uint inputs:execIn.connect = </World/water/animate_water/on_tick.outputs:tick>
                custom token inputs:layerIdentifier
                custom token inputs:name = "inputs:texture_rotate"
                custom rel inputs:prim = </World/Looks/Water_Opaque_01/Shader> (
                    customData = {
                        dictionary omni = {
                            dictionary graph = {
                                string relType = "bundle"
                            }
                        }
                    }
                )
                custom token inputs:primPath
                custom bool inputs:usdWriteBack = 1
                custom bool inputs:usePath = 0
                custom token inputs:value
                prepend token inputs:value.connect = </World/water/animate_water/multiply.outputs:product>
                token node:type = "omni.graph.nodes.WritePrimAttribute"
                int node:typeVersion = 3
                custom uint outputs:execOut (
                    customData = {
                        bool isExecution = 1
                    }
                )
                custom bool state:correctlySetup
                custom uint64 state:destAttrib
                custom uint64 state:destPath
                custom uint64 state:destPathToken
                custom token state:layerIdentifier
                custom token state:resolvedLayerIdentifier
                uniform token ui:nodegraph:node:expansionState = "open"
                uniform float2 ui:nodegraph:node:pos = (915.9052, 9.446399)
            }

            def OmniGraphNode "to_float" (
                prepend apiSchemas = ["NodeGraphNodeAPI"]
            )
            {
                custom token inputs:role (
                    allowedTokens = ["None", "Color", "Normal", "Point", "Quaternion", "TexCoord", "Vector"]
                )
                custom token inputs:value
                prepend token inputs:value.connect = </World/water/animate_water/on_tick.outputs:absoluteSimTime>
                token node:type = "omni.graph.nodes.ToFloat"
                int node:typeVersion = 1
                custom token outputs:converted
                uniform token ui:nodegraph:node:expansionState = "open"
                uniform float2 ui:nodegraph:node:pos = (430.84155, 196.75458)
            }

            def OmniGraphNode "on_tick" (
                prepend apiSchemas = ["NodeGraphNodeAPI"]
            )
            {
                custom uint inputs:framePeriod = 0
                custom bool inputs:onlyPlayback = 1
                token node:type = "omni.graph.action.OnTick"
                int node:typeVersion = 2
                custom double outputs:absoluteSimTime
                custom double outputs:deltaSeconds
                custom double outputs:frame
                custom bool outputs:isPlaying
                custom uint outputs:tick (
                    customData = {
                        bool isExecution = 1
                    }
                )
                custom double outputs:time
                custom double outputs:timeSinceStart
                custom double state:accumulatedSeconds
                custom uint state:frameCount
                uniform token ui:nodegraph:node:expansionState = "open"
                uniform float2 ui:nodegraph:node:pos = (151.8729, 22.330576)
            }

            def OmniGraphNode "constant_float" (
                prepend apiSchemas = ["NodeGraphNodeAPI"]
            )
            {
                custom float inputs:value = 1.5
                token node:type = "omni.graph.nodes.ConstantFloat"
                int node:typeVersion = 1
                uniform token ui:nodegraph:node:expansionState = "open"
                uniform float2 ui:nodegraph:node:pos = (427.76306, 329.1448)
            }

            def OmniGraphNode "multiply" (
                prepend apiSchemas = ["NodeGraphNodeAPI"]
            )
            {
                custom token inputs:a
                prepend token inputs:a.connect = </World/water/animate_water/to_float.outputs:converted>
                custom token inputs:b
                prepend token inputs:b.connect = </World/water/animate_water/constant_float.inputs:value>
                token node:type = "omni.graph.nodes.Multiply"
                int node:typeVersion = 2
                custom token outputs:product
                uniform token ui:nodegraph:node:expansionState = "open"
                uniform float2 ui:nodegraph:node:pos = (639.59625, 248.49762)
            }
        }
    }

    def DistantLight "DistantLight" (
        prepend apiSchemas = ["ShapingAPI"]
    )
    {
        float inputs:angle = 1
        float inputs:intensity = 3000
        float inputs:shaping:cone:angle = 180
        float inputs:shaping:cone:softness
        float inputs:shaping:focus
        color3f inputs:shaping:focusTint
        asset inputs:shaping:ies:file
        quatd xformOp:orient = (0.6532814824381883, 0.2705980500730985, 0.27059805007309845, 0.6532814824381882)
        double3 xformOp:scale = (1, 1, 1)
        double3 xformOp:translate = (0, 0, 0)
        uniform token[] xformOpOrder = ["xformOp:translate", "xformOp:orient", "xformOp:scale"]
    }
}

def Xform "Environment"
{
    quatd xformOp:orient = (1, 0, 0, 0)
    double3 xformOp:scale = (1, 1, 1)
    double3 xformOp:translate = (0, 0, 0)
    uniform token[] xformOpOrder = ["xformOp:translate", "xformOp:orient", "xformOp:scale"]

    def DistantLight "defaultLight" (
        prepend apiSchemas = ["ShapingAPI"]
    )
    {
        float angle = 1
        float intensity = 3000
        float shaping:cone:angle = 180
        float shaping:cone:softness
        float shaping:focus
        color3f shaping:focusTint
        asset shaping:ies:file
        quatd xformOp:orient = (0.6532814824381883, 0.2705980500730985, 0.27059805007309845, 0.6532814824381882)
        double3 xformOp:scale = (1, 1, 1)
        double3 xformOp:translate = (0, 0, 0)
        uniform token[] xformOpOrder = ["xformOp:translate", "xformOp:orient", "xformOp:scale"]
    }
}

