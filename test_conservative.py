#!/usr/bin/env python3
"""
ROV仿真保守测试 - 使用极低的力限制来避免"飞走"问题
"""

# Isaac Sim标准初始化代码
from isaacsim import SimulationApp
simulation_app = SimulationApp({"headless": False})

import os
import carb
from rov_standalone import ROVSimulation


def main():
    """保守测试主函数"""
    
    print("🧪 ROV保守测试启动")
    print("=" * 50)
    print("🎯 目标：验证浮力物理是否正常工作")
    print("🔧 策略：使用极低的力限制，避免物体飞走")
    print()
    
    # ========================================
    # 🛡️ 超保守配置 - 防止物体飞走
    # ========================================
    
    conservative_config = {
        # 文件路径
        'usd_file_path': "/home/<USER>/self_underwater_isaacsim/BUOYANCY_TEST.usd",
        
        # 物体属性 - 明显的上浮配置
        'object_mass': 0.3,         # 很轻的质量
        'object_volume': 1.0,       # 标准体积
        'object_height': 1.0,       # 标准高度
        
        # 环境参数
        'water_density': 1.0,       # 标准水密度
        'gravity': 9.8,             # 标准重力
        
        # 超保守的PID参数
        'pid_kp': 1.0,              # 极低的比例增益
        'pid_ki': 0.1,              # 极低的积分增益
        'pid_kd': 0.05,             # 极低的微分增益
        'pid_sat_max': 3.0,         # 极低的输出上限
        'pid_sat_min': -3.0,        # 极低的输出下限
        
        # 阻尼参数
        'max_damping': 1.0,         # 适中的阻尼
        
        # 极严格的力限制
        'max_buoyancy_force': 15.0, # 限制浮力
        'max_thruster_force': 5.0,  # 极低的推进器力
        'max_controller_force': 3.0,# 极低的控制器力
        'max_total_force': 12.0,    # 极低的总力限制
        
        # 安全参数
        'water_surface_z': 0.0,     # 水面高度
        'reset_threshold_z': 20.0,  # 降低重置阈值
        
        # 调试
        'debug_mode': True
    }
    
    # ========================================
    # 📊 理论验证
    # ========================================
    
    mass = conservative_config['object_mass']
    volume = conservative_config['object_volume']
    water_density = conservative_config['water_density']
    gravity = conservative_config['gravity']
    
    object_density = mass / volume
    theoretical_buoyancy = water_density * volume * gravity
    theoretical_weight = mass * gravity
    net_force = theoretical_buoyancy - theoretical_weight
    
    print(f"📊 保守配置理论分析:")
    print(f"   物体密度: {object_density:.2f} kg/m³")
    print(f"   水密度: {water_density:.2f} kg/m³")
    print(f"   理论浮力: {theoretical_buoyancy:.2f}N ↑")
    print(f"   理论重力: {theoretical_weight:.2f}N ↓")
    print(f"   理论净力: {net_force:.2f}N ({'向上' if net_force > 0 else '向下'})")
    print(f"   预期行为: {'上浮 ⬆️' if object_density < water_density else '下沉 ⬇️'}")
    print(f"   最大总力限制: {conservative_config['max_total_force']:.1f}N")
    print()
    
    if net_force > conservative_config['max_total_force']:
        print(f"⚠️  警告：理论净力({net_force:.1f}N) > 最大总力限制({conservative_config['max_total_force']:.1f}N)")
        print(f"   系统将限制实际应用的力，可能影响上浮速度")
        print()
    
    # ========================================
    # 🚀 启动仿真
    # ========================================
    
    # 创建ROV仿真实例
    rov_sim = ROVSimulation()
    
    # 应用保守配置
    rov_sim.apply_config(conservative_config)
    
    # 加载USD场景
    usd_path = os.path.abspath(conservative_config['usd_file_path'])
    if not rov_sim.load_usd_scene(usd_path):
        carb.log_error("无法加载USD场景，退出")
        simulation_app.close()
        return

    # 设置物理对象
    if not rov_sim.setup_physics_object():
        carb.log_error("❌ 无法设置物理对象，退出")
        simulation_app.close()
        return

    print("🎉 保守测试启动成功！")
    print("👀 观察要点：")
    print("   1. 物体是否缓慢上浮（而不是飞走）")
    print("   2. 调试信息中的力值是否在合理范围内")
    print("   3. 如果物体超过20m高度，会自动重置")
    print("   4. 如果仍然飞走，说明需要进一步降低力限制")
    print()

    # 仿真循环
    try:
        while simulation_app.is_running():
            rov_sim.step()
    except KeyboardInterrupt:
        print("用户中断仿真")
    except Exception as e:
        carb.log_error(f"仿真错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        simulation_app.close()


if __name__ == "__main__":
    main()
