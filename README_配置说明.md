# ROV仿真配置说明

## 🎯 快速开始

### 方法1: 使用简化主程序（推荐）
```bash
python rov_main_simple.py
```

在 `rov_main_simple.py` 的顶部选择配置方案：
```python
# 选择其中一个配置方案
config = ROVPresets.get_floating_cube()    # 上浮方块
# config = ROVPresets.get_sinking_cube()   # 下沉方块  
# config = ROVPresets.get_neutral_cube()   # 中性浮力
# config = ROVPresets.get_seawater_test()  # 海水测试
```

### 方法2: 使用原始主程序
```bash
python rov_standalone.py
```

## 🎛️ 参数配置

### 在 `rov_config.py` 中修改参数：

#### 🧊 物体属性
```python
OBJECT_MASS = 0.4           # kg - 物体质量
OBJECT_VOLUME = 1.0         # m³ - 物体体积  
OBJECT_HEIGHT = 1.0         # m - 物体高度
```

#### 🌊 水环境
```python
WATER_DENSITY = 1.0         # kg/m³ (1.0=淡水, 1.025=海水)
GRAVITY = 9.8               # m/s² - 重力加速度
```

#### 🎮 控制器
```python
PID_KP = 10.0               # 比例增益
PID_KI = 1.0                # 积分增益
PID_KD = 0.1                # 微分增益
```

#### 🚀 力限制（安全保护）
```python
MAX_BUOYANCY_FORCE = 50.0   # N - 最大浮力
MAX_THRUSTER_FORCE = 20.0   # N - 最大推进器力
MAX_TOTAL_FORCE = 100.0     # N - 最大总力
```

## 🧪 测试方案

### 1. 上浮测试
- 质量: 0.4kg (密度 0.4 < 1.0)
- 预期: 方块上浮到水面

### 2. 下沉测试  
- 质量: 1.5kg (密度 1.5 > 1.0)
- 预期: 方块下沉

### 3. 中性浮力测试
- 质量: 1.0kg (密度 1.0 = 1.0)  
- 预期: 方块悬浮

### 4. 海水测试
- 质量: 0.4kg, 水密度: 1.025kg/m³
- 预期: 更强的上浮力

## 🔧 常见调整

### 让方块浮得更快
```python
OBJECT_MASS = 0.2           # 减小质量
# 或
WATER_DENSITY = 1.2         # 增加水密度
```

### 让方块下沉
```python
OBJECT_MASS = 1.5           # 增加质量
```

### 调整控制响应
```python
PID_KP = 20.0               # 增加响应速度
PID_KD = 0.5                # 增加阻尼
```

### 增加安全限制
```python
MAX_TOTAL_FORCE = 50.0      # 降低最大力
```

## 🐛 调试

### 开启详细调试信息
```python
ENABLE_DEBUG = True
```

### 查看力分析
调试输出会显示：
- 浸没体积和高度
- 浮力和重力分量  
- 净力大小和方向
- 物体质量验证

## 📊 理论验证

程序启动时会显示理论计算：
```
📊 当前配置:
   物体: 质量=0.4kg, 体积=1.0m³
   密度: 物体=0.40kg/m³, 水=1.00kg/m³  
   理论力: 浮力=9.80N↑, 重力=3.92N↓
   净力: 5.88N (向上)
   预期行为: 上浮⬆️
```

## 🚨 故障排除

### 方块不动
1. 检查质量设置是否生效
2. 确认Isaac Sim重力已禁用
3. 查看调试输出中的力值

### 方块飞走
1. 降低 `MAX_TOTAL_FORCE`
2. 检查PID参数是否过大
3. 确认物体质量不为0

### 行为与预期不符
1. 验证密度计算: 密度 = 质量/体积
2. 检查水密度设置
3. 确认浮力计算公式: 浮力 = 水密度 × 体积 × 重力
