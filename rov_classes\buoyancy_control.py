"""
浮力控制模块 - 简单的浮力计算
简化版本：移除BaseNode架构，改为直接函数调用
"""


def calculate_buoyancy_control(volume, height, z_position, 
                             water_density=1.0, gravity=9.8):
    """
    计算简单浮力的函数（不考虑旋转）
    
    Args:
        volume: 物体体积 (m³)
        height: 物体高度 (m)
        z_position: 物体Z位置 (m)
        water_density: 水密度 (kg/m³)
        gravity: 重力加速度 (m/s²)
    
    Returns:
        dict: {
            'z_force': Z方向的浮力 (N)
        }
    """
    # 计算浸没体积
    submerged_height = _calculate_submerged_height(z_position, height)
    submerged_volume = volume * submerged_height
    
    # 计算浮力
    buoyancy_force = water_density * submerged_volume * gravity
    
    return {'z_force': buoyancy_force}


def _calculate_submerged_height(z_position, height):
    """计算物体的浸没高度比例"""
    center_of_h = height / 2
    if z_position >= center_of_h:
        return 0.0
    elif z_position < -center_of_h:
        return 1.0
    else:
        return (center_of_h - z_position) / height
