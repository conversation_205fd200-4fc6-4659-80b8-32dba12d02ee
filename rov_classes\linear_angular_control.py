"""
线性角度控制模块 - 推进器控制
简化版本：移除BaseNode架构，改为直接函数调用
"""


def calculate_thruster_forces(y_stick, x_stick):
    """
    基于摇杆输入计算四个推进器线性力的函数
    
    该功能接收x和y摇杆值，并计算四个推进器的适当推力值，
    配置允许线性和角运动控制。
    
    Args:
        y_stick: Y轴摇杆值，用于前进/后退运动
        x_stick: X轴摇杆值，用于旋转运动
    
    Returns:
        dict: {
            'left_front': 左前推进器力向量 [x,y,z],
            'right_front': 右前推进器力向量 [x,y,z],
            'left_back': 左后推进器力向量 [x,y,z],
            'right_back': 右后推进器力向量 [x,y,z]
        }
    """
    # 计算线性力
    # 推进器配置：
    # - y_stick控制前进/后退
    # - x_stick控制左右旋转
    # - 前推进器和后推进器方向相反以实现旋转
    
    return {
        'left_front': [0, 0, y_stick + x_stick],
        'right_front': [0, 0, y_stick - x_stick],
        'left_back': [0, 0, -y_stick - x_stick],
        'right_back': [0, 0, -y_stick + x_stick]
    }
