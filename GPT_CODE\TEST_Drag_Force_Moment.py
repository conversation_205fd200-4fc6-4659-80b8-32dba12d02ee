import numpy as np
import matplotlib.pyplot as plt

# --- 1. 定义航行器和环境的物理参数 (参考论文表3) ---
# 航行器几何参数
VEHICLE_HEIGHT = 0.3  # 航行器总高度 H (m)
RHO_WATER = 1025      # 水的密度 (kg/m^3)

# 阻力系数矩阵 (来自论文表3，这里只用z方向)
# DM = diag(Xu, Yv, Zw) 其中 Zw = -129 N/(m/s)^2
# 注意论文中的系数是负数，因为他们把负号包含进去了
# 我们在这里使用正值，然后在公式中加负号，更符合物理直觉
Zw_coeff = 129.0

# 模拟参数
V_Z = -0.5  # 航行器垂直下潜速度 (m/s, 负号表示向下)
SIM_TIME = 4  # 总模拟时间 (s)
DT = 0.01     # 时间步长 (s)

# --- 2. 核心计算函数 ---

def calculate_ks_and_H_bar(z_center, H):
    """
    计算归一化的浸没高度 H_bar 和 损失系数 ks.
    z=0 是水面, z>0 是空中.
    """
    z_bottom = z_center - H / 2
    z_top = z_center + H / 2

    if z_bottom >= 0:
        # 完全在空中
        submerged_height = 0
    elif z_top <= 0:
        # 完全在水下
        submerged_height = H
    else:
        # 过渡阶段
        submerged_height = -z_bottom
    
    H_bar = submerged_height / H
    
    # 根据公式(6)计算ks
    ks = np.clip(H_bar, 0, 1) # np.clip能优雅地实现分段函数
    
    return H_bar, ks

def calculate_z_drag(velocity_z, ks, z_drag_coeff):
    """
    计算Z轴方向上的阻力
    公式: Fz = -ks * Zw * w|w|
    """
    if ks == 0:
        return 0.0
    
    # 论文中 Zw 是 -129，所以 Fz = ks * Zw * w|w|
    # 如果我们用 Zw_coeff = 129, Fz = -ks * Zw_coeff * w|w|
    drag_force_z = -ks * z_drag_coeff * velocity_z * np.abs(velocity_z)
    return drag_force_z

# --- 3. 运行模拟 ---

# 初始化数组来存储结果
time_steps = np.arange(0, SIM_TIME, DT)
z_positions = np.zeros_like(time_steps)
drag_forces_z = np.zeros_like(time_steps)
ks_values = np.zeros_like(time_steps)

# 设置初始位置 (航行器底部刚好在水面上方)
z0 = VEHICLE_HEIGHT / 2 + 0.1
z_positions[0] = z0

for i in range(1, len(time_steps)):
    # 更新位置 (简单的匀速运动)
    z_positions[i] = z_positions[i-1] + V_Z * DT
    
    # 计算当前时刻的 ks
    _, ks = calculate_ks_and_H_bar(z_positions[i], VEHICLE_HEIGHT)
    ks_values[i] = ks
    
    # 计算当前时刻的阻力
    drag_forces_z[i] = calculate_z_drag(V_Z, ks, Zw_coeff)

# --- 4. 可视化结果 ---

fig, ax1 = plt.subplots(figsize=(12, 7))

# 绘制Z轴阻力
color = 'tab:red'
ax1.set_xlabel('Time (s)')
ax1.set_ylabel('Drag Force on Z-axis (N)', color=color)
ax1.plot(time_steps, drag_forces_z, color=color, linewidth=2, label='Z-axis Drag Force (Fd_z)')
ax1.tick_params(axis='y', labelcolor=color)
ax1.grid(True, which='both', linestyle='--', linewidth=0.5)

# 创建第二个Y轴来绘制航行器位置和ks系数
ax2 = ax1.twinx()
color = 'tab:blue'
ax2.set_ylabel('Z-position & ks value', color=color)
ax2.plot(time_steps, z_positions, color=color, linestyle='--', label='Vehicle Z-position (m)')
ax2.plot(time_steps, ks_values, color='tab:green', linestyle=':', linewidth=2, label='Loss Coefficient (ks)')
ax2.tick_params(axis='y', labelcolor=color)

# 标记水面和过渡区域
ax2.axhline(0, color='cyan', linestyle='-', linewidth=2, label='Water Surface (z=0)')
z_transition_top = VEHICLE_HEIGHT / 2
z_transition_bottom = -VEHICLE_HEIGHT / 2
ax2.fill_between(time_steps, z_transition_bottom, z_transition_top, color='cyan', alpha=0.1, label='Transition Zone')

# 图例和标题
fig.legend(loc="upper right", bbox_to_anchor=(1,1), bbox_transform=ax1.transAxes)
plt.title('HAUV Drag Force Simulation during Water Entry')
fig.tight_layout()
plt.show()