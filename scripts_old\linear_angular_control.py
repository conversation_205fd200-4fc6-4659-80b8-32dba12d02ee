"""
ROV线性和角度控制模块
为水下机器人(ROV)生成线性和角度运动控制力
实现基于操纵杆输入的四推进器控制算法

重构版本：同时支持 ActionGraph 模式和纯 Python 调用
"""

# ==================== 纯 Python 接口 ====================

class ThrusterController:
    """
    推进器控制器类（纯 Python 版本）

    实现四推进器配置的控制算法
    """

    def __init__(self, max_thrust=1000.0):
        """
        初始化推进器控制器

        参数:
            max_thrust: 单个推进器最大推力 (N)
        """
        self.max_thrust = max_thrust

    def calculate_thruster_forces(self, linear_input, angular_input):
        """
        根据线性和角度输入计算四个推进器的推力

        参数:
            linear_input: 线性运动输入 [x, y, z]，通常只使用 y（前进/后退）
            angular_input: 角度运动输入 [roll, pitch, yaw]，通常只使用 yaw（左转/右转）

        返回:
            dict: 推进器力向量
                {
                    'left_front': [x, y, z],
                    'right_front': [x, y, z],
                    'left_back': [x, y, z],
                    'right_back': [x, y, z]
                }
        """
        # 提取主要控制输入
        forward_thrust = linear_input[1] if len(linear_input) > 1 else 0.0  # Y轴：前进/后退
        yaw_thrust = angular_input[2] if len(angular_input) > 2 else 0.0    # Yaw：左转/右转

        # 计算各推进器推力
        # 推力分配算法：
        # - 前进/后退：前后推进器反向推力
        # - 左转/右转：左右推进器差动推力

        left_front_thrust = forward_thrust + yaw_thrust
        right_front_thrust = forward_thrust - yaw_thrust
        left_back_thrust = -forward_thrust - yaw_thrust
        right_back_thrust = -forward_thrust + yaw_thrust

        # 推力限制
        thrusts = [left_front_thrust, right_front_thrust, left_back_thrust, right_back_thrust]
        limited_thrusts = [max(-self.max_thrust, min(self.max_thrust, t)) for t in thrusts]

        return {
            'left_front': [0, 0, limited_thrusts[0]],
            'right_front': [0, 0, limited_thrusts[1]],
            'left_back': [0, 0, limited_thrusts[2]],
            'right_back': [0, 0, limited_thrusts[3]]
        }

def calculate_thruster_allocation(y_stick, x_stick, max_thrust=1000.0):
    """
    根据操纵杆输入计算推进器分配（简化版本）

    参数:
        y_stick: Y轴操纵杆值，控制前进/后退运动
        x_stick: X轴操纵杆值，控制左转/右转运动
        max_thrust: 最大推力限制

    返回:
        dict: 推进器力向量
    """
    controller = ThrusterController(max_thrust)
    return controller.calculate_thruster_forces([0, y_stick, 0], [0, 0, x_stick])

def calculate_6dof_thruster_forces(linear_cmd, angular_cmd, thruster_config=None):
    """
    计算6自由度推进器力分配（高级版本）

    参数:
        linear_cmd: 线性运动命令 [x, y, z] (m/s)
        angular_cmd: 角度运动命令 [roll, pitch, yaw] (rad/s)
        thruster_config: 推进器配置字典，包含位置和方向信息

    返回:
        dict: 详细的推进器分配结果
    """
    # 默认四推进器配置
    if thruster_config is None:
        thruster_config = {
            'left_front': {'position': [-0.5, 0.5, 0], 'direction': [0, 0, 1]},
            'right_front': {'position': [0.5, 0.5, 0], 'direction': [0, 0, 1]},
            'left_back': {'position': [-0.5, -0.5, 0], 'direction': [0, 0, 1]},
            'right_back': {'position': [0.5, -0.5, 0], 'direction': [0, 0, 1]}
        }

    # 简化的力分配（实际应用中可能需要更复杂的矩阵运算）
    controller = ThrusterController()
    basic_forces = controller.calculate_thruster_forces(linear_cmd, angular_cmd)

    # 添加配置信息
    result = basic_forces.copy()
    result['thruster_config'] = thruster_config
    result['linear_command'] = linear_cmd
    result['angular_command'] = angular_cmd

    return result

# ==================== ActionGraph 兼容接口 ====================

def setup(db):
    """
    初始化函数（ActionGraph 兼容）
    设置必要的变量或数据结构

    参数:
        db: 数据库对象，用于存储和传递数据

    注意:
        此模块不需要特殊的初始化操作
    """
    pass  # 此情况下不需要初始化操作

def compute(db):
    """
    根据操纵杆输入计算四个推进器的推力（ActionGraph 兼容版本）

    参数:
        db: 数据库对象，包含输入和输出数据
            输入参数:
                y_stick: Y轴操纵杆值，控制前进/后退运动
                x_stick: X轴操纵杆值，控制旋转运动
            输出参数:
                left_front: 左前推进器力向量 [x,y,z]
                right_front: 右前推进器力向量 [x,y,z]
                left_back: 左后推进器力向量 [x,y,z]
                right_back: 右后推进器力向量 [x,y,z]
    """
    # 获取操纵杆输入
    y_stick = db.inputs.y_stick
    x_stick = db.inputs.x_stick

    # 使用纯 Python 函数计算
    result = calculate_thruster_allocation(y_stick, x_stick)

    # 输出结果
    db.outputs.left_front = result['left_front']
    db.outputs.right_front = result['right_front']
    db.outputs.left_back = result['left_back']
    db.outputs.right_back = result['right_back']

# ==================== 测试和演示函数 ====================

def test_thruster_controller():
    """测试推进器控制模块"""
    print("🧪 测试推进器控制模块 (linear_angular_control.py)")
    print("="*50)

    print("📖 模块功能说明:")
    print("   - 实现四推进器配置的控制算法")
    print("   - 支持前进/后退和左转/右转运动")
    print("   - 提供推力分配和限制功能")
    print("   - 支持6自由度运动控制")
    print("   - 同时支持 ActionGraph 和纯 Python 调用")

    # 1. 测试基础推进器控制器
    print(f"\n1️⃣ 测试基础推进器控制器:")
    controller = ThrusterController(max_thrust=1000.0)

    # 测试不同的运动模式
    test_commands = [
        {'linear': [0, 0.5, 0], 'angular': [0, 0, 0], 'desc': '纯前进'},
        {'linear': [0, -0.3, 0], 'angular': [0, 0, 0], 'desc': '纯后退'},
        {'linear': [0, 0, 0], 'angular': [0, 0, 0.4], 'desc': '纯右转'},
        {'linear': [0, 0, 0], 'angular': [0, 0, -0.4], 'desc': '纯左转'},
        {'linear': [0, 0.3, 0], 'angular': [0, 0, 0.2], 'desc': '前进+右转'}
    ]

    for cmd in test_commands:
        result = controller.calculate_thruster_forces(cmd['linear'], cmd['angular'])

        print(f"   {cmd['desc']} (线性={cmd['linear']}, 角度={cmd['angular']}):")
        print(f"     左前: {result['left_front'][2]:.1f}N")
        print(f"     右前: {result['right_front'][2]:.1f}N")
        print(f"     左后: {result['left_back'][2]:.1f}N")
        print(f"     右后: {result['right_back'][2]:.1f}N")

    # 2. 测试操纵杆分配函数
    print(f"\n2️⃣ 测试操纵杆分配函数:")

    joystick_inputs = [
        {'y': 1.0, 'x': 0.0, 'desc': '全速前进'},
        {'y': 0.0, 'x': 1.0, 'desc': '全速右转'},
        {'y': 0.5, 'x': 0.3, 'desc': '中速前进+轻微右转'},
        {'y': -0.8, 'x': -0.5, 'desc': '快速后退+左转'}
    ]

    for joy in joystick_inputs:
        result = calculate_thruster_allocation(joy['y'], joy['x'])

        print(f"   {joy['desc']} (Y={joy['y']}, X={joy['x']}):")
        print(f"     左前: {result['left_front'][2]:.1f}N")
        print(f"     右前: {result['right_front'][2]:.1f}N")
        print(f"     左后: {result['left_back'][2]:.1f}N")
        print(f"     右后: {result['right_back'][2]:.1f}N")

        # 计算总推力
        total_thrust = sum([
            abs(result['left_front'][2]),
            abs(result['right_front'][2]),
            abs(result['left_back'][2]),
            abs(result['right_back'][2])
        ])
        print(f"     总推力: {total_thrust:.1f}N")

def test_6dof_control():
    """测试6自由度控制"""
    print(f"\n3️⃣ 测试6自由度控制:")

    # 测试复杂的6DOF运动命令
    test_6dof_commands = [
        {
            'linear': [0.1, 0.3, -0.2],  # 右移+前进+下潜
            'angular': [0.1, 0.0, 0.2],  # 横滚+偏航
            'desc': '复合运动1'
        },
        {
            'linear': [0.0, 0.0, 0.5],   # 纯垂直运动
            'angular': [0.0, 0.3, 0.0],  # 纯俯仰
            'desc': '垂直+俯仰'
        }
    ]

    for cmd in test_6dof_commands:
        result = calculate_6dof_thruster_forces(cmd['linear'], cmd['angular'])

        print(f"   {cmd['desc']}:")
        print(f"     线性命令: {cmd['linear']}")
        print(f"     角度命令: {cmd['angular']}")
        print(f"     推进器分配:")
        for thruster_name, force in result.items():
            if thruster_name not in ['thruster_config', 'linear_command', 'angular_command']:
                print(f"       {thruster_name}: {force}")

def demo_usage_examples():
    """演示使用示例"""
    print(f"\n📚 使用示例:")
    print("="*30)

    print("\n💡 示例 1: 创建推进器控制器")
    print("```python")
    print("from scripts.linear_angular_control import ThrusterController")
    print("")
    print("# 创建推进器控制器")
    print("controller = ThrusterController(max_thrust=1500.0)")
    print("")
    print("# 计算推进器力")
    print("forces = controller.calculate_thruster_forces(")
    print("    linear_input=[0, 0.5, 0],    # 前进")
    print("    angular_input=[0, 0, 0.3]    # 右转")
    print(")")
    print("```")

    # 实际运行示例
    controller = ThrusterController(max_thrust=1500.0)
    forces = controller.calculate_thruster_forces([0, 0.5, 0], [0, 0, 0.3])
    print(f"➡️ 输出: 左前推力 = {forces['left_front'][2]:.1f} N")
    print(f"➡️ 输出: 右前推力 = {forces['right_front'][2]:.1f} N")

    print("\n💡 示例 2: 操纵杆控制")
    print("```python")
    print("from scripts.linear_angular_control import calculate_thruster_allocation")
    print("")
    print("# 模拟操纵杆输入")
    print("result = calculate_thruster_allocation(")
    print("    y_stick=0.7,    # 前进70%")
    print("    x_stick=0.2     # 右转20%")
    print(")")
    print("```")

    # 实际运行示例
    result = calculate_thruster_allocation(y_stick=0.7, x_stick=0.2)
    print(f"➡️ 输出: 推进器分配完成")
    print(f"   左前: {result['left_front'][2]:.1f}N, 右前: {result['right_front'][2]:.1f}N")

    print("\n💡 示例 3: 6DOF 控制")
    print("```python")
    print("from scripts.linear_angular_control import calculate_6dof_thruster_forces")
    print("")
    print("result = calculate_6dof_thruster_forces(")
    print("    linear_cmd=[0.1, 0.3, -0.2],")
    print("    angular_cmd=[0.0, 0.1, 0.2]")
    print(")")
    print("```")

    # 实际运行示例
    result = calculate_6dof_thruster_forces([0.1, 0.3, -0.2], [0.0, 0.1, 0.2])
    print(f"➡️ 输出: 6DOF 推进器分配完成")

if __name__ == "__main__":
    """当直接运行此文件时执行测试和演示"""
    test_thruster_controller()
    test_6dof_control()
    demo_usage_examples()
    print("\n✅ 推进器控制模块测试完成！")
    print("\n🔗 相关模块:")
    print("   - controller.py: PID 控制")
    print("   - damping.py: 阻尼计算")
    print("   - rov_physics_unified.py: 统一物理引擎")
