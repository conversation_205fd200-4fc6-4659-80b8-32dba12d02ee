"""
PID控制器模块
简化版本：移除BaseNode架构，改为直接函数调用
"""


class PIDController:
    """简化的PID控制器类"""
    
    def __init__(self, kp=100, ki=10, kd=0.01, sat_max=1000, sat_min=-1000):
        """初始化PID控制器参数"""
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.sat_max = sat_max
        self.sat_min = sat_min
        
        # PID状态变量
        self.error_integral = 0
        self.error_prev = 0
        self.time = 0.01667  # 约60Hz

    def reset(self):
        """重置PID控制器状态"""
        self.error_integral = 0
        self.error_prev = 0
        print("🔄 PID控制器状态已重置")
    
    def calculate_control(self, orientation, dive_force=0.0):
        """
        计算PID控制输出
        
        Args:
            orientation: 当前方向测量值
            dive_force: 额外的垂直力
        
        Returns:
            dict: {
                'force': 正控制力向量 [x,y,z],
                'minus_force': 负控制力向量 [x,y,z]
            }
        """
        # 计算误差（目标为0方向）
        error = 0 - orientation
        self.error_integral += error
        
        # PID控制计算
        control_output = (self.kp * error + 
                         self.ki * (self.error_integral) * self.time + 
                         self.kd * (error - self.error_prev) / self.time)
        
        # 饱和限制
        if control_output > self.sat_max:
            control_output = self.sat_max
        elif control_output < self.sat_min:
            control_output = self.sat_min
        
        # 更新前一次误差
        self.error_prev = error
        
        return {
            'force': [0, 0, control_output + dive_force],
            'minus_force': [0, 0, -control_output + dive_force]
        }
    
    def reset(self):
        """重置PID控制器状态"""
        self.error_integral = 0
        self.error_prev = 0


def calculate_controller_output(orientation, dive_force=0.0, controller=None):
    """
    便捷函数：计算PID控制输出
    
    Args:
        orientation: 当前方向测量值
        dive_force: 额外的垂直力
        controller: PID控制器实例，如果为None则创建新的
    
    Returns:
        dict: 控制输出结果
    """
    if controller is None:
        controller = PIDController()
    
    return controller.calculate_control(orientation, dive_force)
