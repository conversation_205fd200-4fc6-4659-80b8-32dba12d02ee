{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: Current File",
            "type": "python",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "env": {
                "RESOURCE_NAME": "IsaacSim"
            },
            "python": "/home/<USER>/isaac-sim/kit/python/bin/python3",
            "envFile": "/home/<USER>/isaac-sim/.vscode/.standalone_examples.env",
            "preLaunchTask": "setup_python_env"
        },
        {
            "name": "Python: Attach (windows-x86_64/linux-x86_64)",
            "type": "python",
            "request": "attach",
            "port": 3000,
            "host": "localhost"
        },
        {
            "name": "(Linux) isaac-sim",
            "type": "cppdbg",
            "request": "launch",
            "program": "/home/<USER>/isaac-sim/kit/kit",
            "args": ["/home/<USER>/isaac-sim/apps/isaacsim.exp.full.kit",
                "--ext-folder", "/home/<USER>/isaac-sim/exts",
                "--ext-folder", "/home/<USER>/isaac-sim/apps"],
            "stopAtEntry": false,
            "cwd": "/home/<USER>/isaac-sim",
            "environment": [],
            "externalConsole": false,
            "MIMode": "gdb",
            "setupCommands": [
                {
                    "description": "Enable pretty-printing for gdb",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                }
            ]
        }
    ]
}