#!/usr/bin/env python3
"""
测试ROV类功能
验证转换后的Python类是否正常工作
"""

import numpy as np
from rov_classes import (
    BuoyancyForcesNode,
    BuoyancyControlNode,
    DampingNode,
    ControllerNode,
    LinearAngularControlNode,
    QuatToEulerNode
)


def test_buoyancy_forces():
    """测试浮力计算（带旋转）"""
    print("=== 测试浮力计算（带旋转） ===")
    
    node = BuoyancyForcesNode("TestBuoyancyForces")
    
    # 设置测试输入
    node.set_inputs(
        volume=0.1,  # 0.1 m³
        height=0.2,  # 0.2 m
        z_position=-0.05,  # 部分浸没
        rotation=[10.0, 5.0, 0.0]  # 10度roll, 5度pitch
    )
    
    # 执行计算
    output = node.execute()
    
    print(f"输入: volume=0.1, height=0.2, z_position=-0.05, rotation=[10,5,0]")
    print(f"输出: x_force={output.get('x_force', 0):.4f}, y_force={output.get('y_force', 0):.4f}, z_force={output.get('z_force', 0):.4f}")
    print()


def test_buoyancy_control():
    """测试简单浮力计算"""
    print("=== 测试简单浮力计算 ===")
    
    node = BuoyancyControlNode("TestBuoyancyControl")
    
    # 设置测试输入
    node.set_inputs(
        Volume=0.1,  # 注意大写V
        height=0.2,
        z_position=-0.1  # 完全浸没
    )
    
    # 执行计算
    output = node.execute()
    
    print(f"输入: Volume=0.1, height=0.2, z_position=-0.1")
    print(f"输出: z_force={output.get('z_force', 0):.4f}")
    print()


def test_damping():
    """测试阻尼计算"""
    print("=== 测试阻尼计算 ===")
    
    node = DampingNode("TestDamping")
    
    # 测试不同位置的阻尼
    test_positions = [0.15, 0.0, -0.05, -0.15]  # 水面上、水面、部分浸没、完全浸没
    
    for z_pos in test_positions:
        node.set_inputs(
            z_position=z_pos,
            max_damping=1.0,
            floating_obj_height=0.2
        )
        
        output = node.execute()
        print(f"z_position={z_pos:6.2f} -> linear_damping={output.get('linear_damping', 0):.4f}, angular_damping={output.get('angular_damping', 0):.4f}")
    
    print()


def test_controller():
    """测试PID控制器"""
    print("=== 测试PID控制器 ===")
    
    node = ControllerNode("TestController")
    
    # 模拟几个控制步骤
    orientations = [5.0, 3.0, 1.0, 0.5, 0.1]  # 逐渐接近目标
    dive_force = -10.0
    
    for i, orientation in enumerate(orientations):
        node.set_inputs(
            orientation=orientation,
            dive_force=dive_force
        )
        
        output = node.execute()
        force = output.get('force', [0, 0, 0])
        minus_force = output.get('minus_force', [0, 0, 0])
        
        print(f"步骤{i+1}: orientation={orientation:4.1f} -> force=[{force[0]:.1f}, {force[1]:.1f}, {force[2]:.1f}], minus_force=[{minus_force[0]:.1f}, {minus_force[1]:.1f}, {minus_force[2]:.1f}]")
    
    print()


def test_thruster_control():
    """测试推进器控制"""
    print("=== 测试推进器控制 ===")
    
    node = LinearAngularControlNode("TestThrusterControl")
    
    # 测试不同的摇杆输入
    test_inputs = [
        (1.0, 0.0),   # 纯前进
        (0.0, 1.0),   # 纯右转
        (-1.0, 0.0),  # 纯后退
        (0.0, -1.0),  # 纯左转
        (0.5, 0.5),   # 前进+右转
    ]
    
    for y_stick, x_stick in test_inputs:
        node.set_inputs(
            y_stick=y_stick,
            x_stick=x_stick
        )
        
        output = node.execute()
        lf = output.get('left_front', [0, 0, 0])
        rf = output.get('right_front', [0, 0, 0])
        lb = output.get('left_back', [0, 0, 0])
        rb = output.get('right_back', [0, 0, 0])
        
        print(f"摇杆({y_stick:4.1f}, {x_stick:4.1f}) -> LF:{lf[2]:5.1f}, RF:{rf[2]:5.1f}, LB:{lb[2]:5.1f}, RB:{rb[2]:5.1f}")
    
    print()


def test_quat_to_euler():
    """测试四元数转欧拉角"""
    print("=== 测试四元数转欧拉角 ===")
    
    node = QuatToEulerNode("TestQuatToEuler")
    
    # 测试几个已知的四元数
    test_quats = [
        [0.0, 0.0, 0.0, 1.0],  # 无旋转
        [0.0, 0.0, 0.7071, 0.7071],  # 90度yaw
        [0.7071, 0.0, 0.0, 0.7071],  # 90度roll
        [0.0, 0.7071, 0.0, 0.7071],  # 90度pitch
    ]
    
    for quat in test_quats:
        node.set_inputs(quaternion=quat)
        output = node.execute()
        rotation = output.get('rotation', [0, 0, 0])
        
        print(f"四元数{quat} -> 欧拉角[{rotation[0]:6.1f}, {rotation[1]:6.1f}, {rotation[2]:6.1f}]度")
    
    print()


def test_integration():
    """测试模块集成"""
    print("=== 测试模块集成 ===")
    
    # 创建所有节点
    quat_node = QuatToEulerNode("QuatToEuler")
    buoyancy_node = BuoyancyForcesNode("BuoyancyForces")
    damping_node = DampingNode("Damping")
    controller_node = ControllerNode("Controller")
    thruster_node = LinearAngularControlNode("ThrusterControl")
    
    # 模拟ROV状态
    rov_quaternion = [0.1, 0.05, 0.0, 0.995]  # 小角度旋转
    rov_z_position = -0.05  # 部分浸没
    rov_volume = 0.1
    rov_height = 0.2
    
    # 模拟控制输入
    joystick_y = 0.5
    joystick_x = 0.2
    dive_force = -5.0
    
    # 1. 转换四元数到欧拉角
    quat_node.set_inputs(quaternion=rov_quaternion)
    quat_output = quat_node.execute()
    rotation = quat_output.get('rotation', [0, 0, 0])
    
    # 2. 计算浮力
    buoyancy_node.set_inputs(
        volume=rov_volume,
        height=rov_height,
        z_position=rov_z_position,
        rotation=rotation
    )
    buoyancy_output = buoyancy_node.execute()
    
    # 3. 计算阻尼
    damping_node.set_inputs(
        z_position=rov_z_position,
        max_damping=1.0,
        floating_obj_height=rov_height
    )
    damping_output = damping_node.execute()
    
    # 4. 计算控制器输出
    pitch_angle = rotation[1]
    controller_node.set_inputs(
        orientation=pitch_angle,
        dive_force=dive_force
    )
    controller_output = controller_node.execute()
    
    # 5. 计算推进器输出
    thruster_node.set_inputs(
        y_stick=joystick_y,
        x_stick=joystick_x
    )
    thruster_output = thruster_node.execute()
    
    # 显示结果
    print("集成测试结果:")
    print(f"ROV状态: z_position={rov_z_position}, rotation={rotation}")
    print(f"浮力: x={buoyancy_output.get('x_force', 0):.3f}, y={buoyancy_output.get('y_force', 0):.3f}, z={buoyancy_output.get('z_force', 0):.3f}")
    print(f"阻尼: linear={damping_output.get('linear_damping', 0):.3f}, angular={damping_output.get('angular_damping', 0):.3f}")
    print(f"控制器: force={controller_output.get('force', [0,0,0])}")
    print(f"推进器: LF={thruster_output.get('left_front', [0,0,0])[2]:.1f}, RF={thruster_output.get('right_front', [0,0,0])[2]:.1f}")
    print()


def main():
    """运行所有测试"""
    print("开始测试ROV类功能...\n")
    
    try:
        test_buoyancy_forces()
        test_buoyancy_control()
        test_damping()
        test_controller()
        test_thruster_control()
        test_quat_to_euler()
        test_integration()
        
        print("所有测试完成！")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
