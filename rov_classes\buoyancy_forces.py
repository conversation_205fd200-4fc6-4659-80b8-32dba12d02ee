"""
浮力计算模块 - 带旋转的浮力计算
简化版本：移除BaseNode架构，改为直接函数调用
"""

import math
import numpy as np


def calculate_buoyancy_forces(volume, height, z_position, rotation,
                            water_density=1.0, gravity=9.8, object_mass=0.4, debug=False):
    """
    计算浮力和重力的净力（因为我们禁用了Isaac Sim的自动重力）

    Args:
        volume: 物体体积 (m³)
        height: 物体高度 (m)
        z_position: 物体Z位置 (m)
        rotation: 旋转角度 [roll, pitch, yaw] (度)
        water_density: 水密度 (kg/m³)
        gravity: 重力加速度 (m/s²)
        object_mass: 物体质量 (kg)
        debug: 是否输出调试信息

    Returns:
        dict: {
            'x_force': X方向的净力 (N),
            'y_force': Y方向的净力 (N),
            'z_force': Z方向的净力 (N) - 浮力减去重力
        }
    """
    # 计算浸没体积
    submerged_height = _calculate_submerged_height(z_position, height)
    submerged_volume = volume * submerged_height

    # 计算浮力大小（向上）
    buoyancy_force = water_density * submerged_volume * gravity

    # 计算重力（向下）
    weight_force = object_mass * gravity

    # 计算净力：浮力 - 重力
    net_z_force = buoyancy_force - weight_force

    # 浮力总是向上的！不应该随物体旋转而改变方向
    # 浮力是环境施加给物体的力，方向固定在世界坐标系的+Z方向
    # 重力总是向下，我们计算净效果

    result = {
        'x_force': 0.0,  # 浮力和重力都不产生X方向的力
        'y_force': 0.0,  # 浮力和重力都不产生Y方向的力
        'z_force': float(net_z_force)  # 净力：正值向上，负值向下
    }
    
    # 调试输出
    if debug:
        print(f"🔧 浮力-重力平衡计算:")
        print(f"   浸没高度: {submerged_height:.3f}m")
        print(f"   浸没体积: {submerged_volume:.3f}m³")
        print(f"   物体质量: {object_mass:.2f}kg")
        print(f"   浮力: {buoyancy_force:.2f}N (向上)")
        print(f"   重力: {weight_force:.2f}N (向下)")
        print(f"   净力: {net_z_force:.2f}N ({'向上' if net_z_force > 0 else '向下' if net_z_force < 0 else '平衡'})")
        print(f"   旋转角度: {rotation} 度")
        print(f"   ✅ 力平衡: 浮力和重力都在世界坐标系中计算")
    
    return result


def _calculate_submerged_height(z_position, height):
    """计算物体的浸没高度比例"""
    center_of_h = height / 2
    if z_position >= center_of_h:
        return 0.0
    elif z_position < -center_of_h:
        return 1.0
    else:
        return (center_of_h - z_position) / height
