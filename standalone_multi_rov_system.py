# Copyright (c) 2021-2024, NVIDIA CORPORATION. All rights reserved.
#
# NVIDIA CORPORATION and its licensors retain all intellectual property
# and proprietary rights in and to this software, related documentation
# and any modifications thereto. Any use, reproduction, disclosure or
# distribution of this software and related documentation without an express
# license agreement from NVIDIA CORPORATION is strictly prohibited.
#

"""
Standalone Multi-ROV System - 独立模式版本
从 ActionGraph 模式转换为独立 Python 脚本
基于官方 Isaac Sim standalone 模式最佳实践
"""

from isaacsim import SimulationApp

simulation_app = SimulationApp({"headless": False})

import carb
import omni
import math
import time
import os
import sys
from isaacsim.core.api import SimulationContext
from pxr import UsdGeom, Gf, UsdPhysics, Usd
# from omni.isaac.dynamic_control import _dynamic_control  # 暂时不使用
# 导入重构后的物理计算模块（必需）

# 添加 scripts 目录到路径，以便导入物理计算模块
current_dir = os.path.dirname(os.path.abspath(__file__))
scripts_dir = os.path.join(current_dir, "scripts")
if scripts_dir not in sys.path:
    sys.path.append(scripts_dir)

try:
    from rov_physics_unified import ROVPhysicsEngine, create_rov_physics_engine, calculate_simple_rov_physics
    print("✅ 成功导入统一物理计算模块")
    USE_UNIFIED_PHYSICS = True
except ImportError as e:
    print(f"❌ 无法导入统一物理模块: {e}")
    print("❌ 统一物理模块是必需的，程序无法继续运行")
    print("💡 请确保 scripts/rov_physics_unified.py 文件存在且可导入")
    USE_UNIFIED_PHYSICS = False
    # 不再继续运行，因为统一物理模块是必需的
    raise ImportError(f"统一物理模块导入失败: {e}") from e


# 全局变量声明（将在 main 函数中初始化）
stage = None
sim_context = None
physx_interface = None

# ⚠️ 重要：加载 USD 环境文件 (类似于手动导入操作)
def load_usd_environment():
    """加载 USD 环境文件 - 替代手动导入操作"""
    print("🌊 加载 USD 环境文件...")

    usd_file_path = "ROV_THRUSTERS.usd"  # 请根据实际路径调整

    try:
        print(f"📁 找到 USD 文件: {usd_file_path}")

        # 获取绝对路径
        abs_path = os.path.abspath(usd_file_path)
        print(f"📍 绝对路径: {abs_path}")

        # 使用 omni.usd 直接打开（最接近双击行为）
        print("🔄 直接打开USD文件作为主场景...")

        # 关闭当前场景
        omni.usd.get_context().close_stage()

        # 直接打开USD文件
        success = omni.usd.get_context().open_stage(abs_path)

        if success:
            print("✅ USD文件已作为主场景打开")

            # 重新获取stage（因为我们打开了新的场景）
            global stage
            stage = omni.usd.get_context().get_stage()

            # 打印场景结构信息
            print("📋 场景结构:")
            root_prim = stage.GetPseudoRoot()
            for child in root_prim.GetChildren():
                print(f"  � /{child.GetName()}")

            return True
        else:
            print("❌ 打开USD文件失败")
            return True

    except Exception as e:
        print(f"❌ 加载 USD 环境失败: {e}")
        return True

# ROV 配置
rov_configs = [
    {
        "name": "ROV_Original",
        "path": "/World/ROV_Original",
        "mass": 20.0,
        "size": 1,
        "target_depth": -1.5,
        "color": (0.8, 0.2, 0.2),  # 红色
        "max_thrust": 150.0,
    }
]

# 全局变量：ROV 物理引擎实例
rov_physics_engines = []

def create_rov_prim(stage, rov_config):
    """创建 ROV prim"""
    rov_path = rov_config["path"]
    rov_name = rov_config["name"]
    size = rov_config["size"]
    mass = rov_config["mass"]
    target_depth = rov_config["target_depth"]
    color = rov_config["color"]

    print(f"🔧 创建 {rov_name} 在 {rov_path}")

    try:
        # 创建立方体几何体
        cube_geom = UsdGeom.Cube.Define(stage, rov_path)
        cube_geom.CreateSizeAttr(size)

        # 安全地设置位置 - 检查是否已存在 translate 操作
        xformable = UsdGeom.Xformable(cube_geom)
        translate_ops = [op for op in xformable.GetOrderedXformOps() if op.GetOpType() == UsdGeom.XformOp.TypeTranslate]

        if translate_ops:
            # 如果已存在 translate 操作，直接设置值
            translate_ops[0].Set(Gf.Vec3f(0.5, 0.5, target_depth))
            print(f"  🔄 使用现有的 translate 操作")
        else:
            # 如果不存在，创建新的 translate 操作
            translate_op = cube_geom.AddTranslateOp()
            translate_op.Set(Gf.Vec3f(0.5, 0.5, target_depth))
            print(f"  🆕 创建新的 translate 操作")

        cube_geom.CreateDisplayColorAttr([color])

        # 获取 prim 并添加物理属性
        prim = cube_geom.GetPrim()

        # 添加刚体 API
        UsdPhysics.RigidBodyAPI.Apply(prim)

        # 添加碰撞 API
        UsdPhysics.CollisionAPI.Apply(prim)

        # 添加质量 API
        mass_api = UsdPhysics.MassAPI.Apply(prim)
        mass_api.CreateMassAttr(mass)

        print(f"  ✅ {rov_name} 创建完成 - 质量: {mass}kg, 尺寸: {size}m, 目标深度: {target_depth}m")

        return prim

    except Exception as e:
        print(f"❌ 创建 {rov_name} 失败: {e}")
        # 尝试更简单的方法
        try:
            print(f"🔄 尝试简化创建方法...")

            # 直接创建 prim
            prim = stage.DefinePrim(rov_path, "Cube")

            # 设置基本属性
            prim.CreateAttribute("size", Usd.ValueTypeNames.Double).Set(size)
            prim.CreateAttribute("primvars:displayColor", Usd.ValueTypeNames.Color3fArray).Set([color])

            # 设置位置
            xform_api = UsdGeom.XformCommonAPI(prim)
            xform_api.SetTranslate(Gf.Vec3f(0.5, 0.5, target_depth))

            # 添加物理属性
            UsdPhysics.RigidBodyAPI.Apply(prim)
            UsdPhysics.CollisionAPI.Apply(prim)
            mass_api = UsdPhysics.MassAPI.Apply(prim)
            mass_api.CreateMassAttr(mass)

            print(f"  ✅ {rov_name} 简化创建完成")
            return prim

        except Exception as e2:
            print(f"❌ 简化创建也失败: {e2}")
            raise e

def get_prim_position(prim):
    """获取 prim 的位置"""
    try:
        # 尝试使用 XformCommonAPI（推荐方法）
        xform_api = UsdGeom.XformCommonAPI(prim)
        translation, _, _ = xform_api.GetTranslate()
        if translation is not None:
            return [translation[0], translation[1], translation[2]]
    except:
        pass

    try:
        # 备用方法：直接获取 xformOp:translate 属性
        xform = prim.GetAttribute("xformOp:translate")
        if xform:
            position = xform.Get()
            if position:
                return [position[0], position[1], position[2]]
    except:
        pass

    try:
        # 第三种方法：通过 Xformable 获取
        xformable = UsdGeom.Xformable(prim)
        ops = xformable.GetOrderedXformOps()
        for op in ops:
            if op.GetOpType() == UsdGeom.XformOp.TypeTranslate:
                position = op.Get()
                if position:
                    return [position[0], position[1], position[2]]
    except:
        pass

    return [0, 0, 0]

def calculate_rov_physics(rov_config, position, last_position, dt, rov_index=0):
    """
    计算 ROV 物理力（仅使用统一物理模块）

    参数:
        rov_config: ROV 配置字典
        position: 当前位置 [x, y, z]
        last_position: 上一帧位置 [x, y, z]
        dt: 时间步长
        rov_index: ROV 索引（用于获取对应的物理引擎）

    返回:
        dict: 包含各种物理力的字典
    """
    # 检查统一物理模块是否可用
    if not USE_UNIFIED_PHYSICS:
        raise RuntimeError("❌ 统一物理模块不可用，无法进行物理计算")

    if rov_index >= len(rov_physics_engines):
        raise RuntimeError(f"❌ ROV 索引 {rov_index} 超出物理引擎数量 {len(rov_physics_engines)}")

    physics_engine = rov_physics_engines[rov_index]
    if physics_engine is None:
        raise RuntimeError(f"❌ ROV {rov_index} 的物理引擎未初始化")

    try:
        # 计算速度
        velocity = [
            (position[0] - last_position[0]) / max(dt, 0.001),
            (position[1] - last_position[1]) / max(dt, 0.001),
            (position[2] - last_position[2]) / max(dt, 0.001)
        ]

        # 使用统一物理模块计算完整物理
        result = physics_engine.calculate_complete_physics(
            position, velocity, last_position, dt
        )

        # 提取物理摘要
        summary = result['physics_summary']

        # 返回标准化格式
        return {
            'total_force': summary['total_force_z'],
            'buoyancy_force': summary['buoyancy_force'],
            'control_force': summary['control_force'],
            'drag_force': summary['drag_force'][2] if isinstance(summary['drag_force'], list) else summary['drag_force'],
            'depth': summary['depth'],
            'detailed_result': result,  # 包含完整的计算结果

            # 额外的详细信息（从统一物理模块获取）
            'buoyancy_details': result.get('buoyancy', {}),
            'control_details': result.get('control', {}),
            'damping_details': result.get('damping', {}),
            'thruster_details': result.get('thruster', {}),
            'environmental_details': result.get('environmental', {})
        }

    except Exception as e:
        # 不再回退到简化计算，直接抛出错误
        error_msg = f"❌ 统一物理计算失败 (ROV {rov_index}): {e}"
        print(error_msg)
        raise RuntimeError(error_msg) from e

def apply_force_to_rov(prim, force_z):
    """向 ROV 应用力"""
    try:
        # 简化版本：直接修改位置（演示用）
        current_pos = get_prim_position(prim)

        # 简单的力积分（这里应该使用更精确的物理积分）
        dt = 1.0 / 60.0  # 假设 60 FPS
        acceleration = force_z / 1000.0  # 假设质量为 1000kg
        velocity_change = acceleration * dt
        new_z = current_pos[2] + velocity_change * dt

        # 更新位置 - 使用多种方法尝试
        try:
            # 方法1：使用 XformCommonAPI（推荐）
            xform_api = UsdGeom.XformCommonAPI(prim)
            xform_api.SetTranslate(Gf.Vec3f(current_pos[0], current_pos[1], new_z))
        except:
            try:
                # 方法2：直接设置属性
                xform = prim.GetAttribute("xformOp:translate")
                if xform:
                    xform.Set(Gf.Vec3f(current_pos[0], current_pos[1], new_z))
            except:
                try:
                    # 方法3：通过 Xformable
                    xformable = UsdGeom.Xformable(prim)
                    ops = xformable.GetOrderedXformOps()
                    for op in ops:
                        if op.GetOpType() == UsdGeom.XformOp.TypeTranslate:
                            op.Set(Gf.Vec3f(current_pos[0], current_pos[1], new_z))
                            break
                except:
                    pass  # 静默处理错误

    except Exception:
        pass  # 静默处理错误


# 简化版本：仅测试环境加载
def test_environment_loading_only():
    """仅测试环境加载功能，保持运行状态"""
    print("🧪 测试模式：仅环境加载")
    print("=" * 50)

    print("\n📋 初始化步骤:")
    print("1. ✅ SimulationApp 已启动")
    print("2. ✅ SimulationContext 已创建")
    print("3. ✅ PhysX 接口已获取")
    print("4. ✅ USD 环境已加载")
    print("5. 🔄 跳过物理仿真和 ROV 创建")

    # 仅启动基础仿真（不创建 ROV）
    print("\n🔄 启动基础仿真环境...")
    try:
        physx_interface.start_simulation()
        print("✅ PhysX 仿真已启动")
    except Exception as e:
        print(f"⚠️ PhysX 启动警告: {e}")

    # 启动时间轴
    try:
        sim_context._timeline.play()
        print("✅ 时间轴已启动")
    except Exception as e:
        print(f"⚠️ 时间轴启动警告: {e}")

    print("\n✅ 环境加载测试完成！")
    print("📋 检查项目:")
    print("  • Isaac Sim 窗口是否打开？")
    print("  • 是否看到了水下环境？")
    print("  • 是否加载了 ROV_THRUSTERS.usd 文件？")
    print("  • 控制台是否显示了正确的加载信息？")

    print("\n🔄 环境将保持运行状态...")
    print("💡 提示:")
    print("  • 您可以在 Isaac Sim 中自由查看环境")
    print("  • 按 Ctrl+C 可以停止程序")
    print("  • 或者直接关闭 Isaac Sim 窗口")
    print("  • 程序会持续更新以保持响应")

    # 持续运行，保持环境活跃
    print("\n⏳ 环境运行中... (按 Ctrl+C 停止)")

    try:
        frame_count = 0
        while True:
            # 保持应用更新
            simulation_app.update()

            # 每 5 秒输出一次状态
            if frame_count % 300 == 0:  # 假设 60 FPS，5秒 = 300帧
                print(f"🔄 环境运行中... 帧数: {frame_count}")

            frame_count += 1

            # 小延迟避免 CPU 占用过高
            import time
            time.sleep(1.0 / 60.0)  # 60 FPS

    except KeyboardInterrupt:
        print("\n🛑 用户中断，停止环境...")
        sim_context._timeline.stop()
        print("✅ 环境已停止")
    except Exception as e:
        print(f"\n❌ 运行时错误: {e}")
        sim_context._timeline.stop()

    return True


# 完整的多ROV仿真函数（重新启用）
def run_multi_rov_simulation():
    """运行完整的多ROV物理仿真"""
    print("🚀 启动多ROV物理仿真系统...")

    print("\n📋 仿真初始化步骤:")
    print("1. ✅ SimulationApp 已启动")
    print("2. ✅ SimulationContext 已创建")
    print("3. ✅ PhysX 接口已获取")
    print("4. ✅ USD 环境已加载")
    print("5. 🔄 正在创建ROV并启动物理仿真...")

    # 启动物理仿真
    try:
        physx_interface.start_simulation()
        physx_interface.force_load_physics_from_usd()
        print("✅ PhysX 仿真已启动")
    except Exception as e:
        print(f"⚠️ PhysX 启动警告: {e}")

    # 初始化统一物理引擎（必需）
    global rov_physics_engines
    rov_physics_engines = []

    print("\n🔧 初始化统一物理引擎:")
    for rov_config in rov_configs:
        try:
            physics_engine = create_rov_physics_engine(
                rov_config["mass"],
                rov_config["size"],
                rov_config["target_depth"],
                rov_config.get("max_thrust", 1000.0)
            )
            rov_physics_engines.append(physics_engine)
            print(f"  ✅ {rov_config['name']}: 统一物理引擎已初始化")
        except Exception as e:
            error_msg = f"❌ {rov_config['name']}: 物理引擎初始化失败: {e}"
            print(f"  {error_msg}")
            # 物理引擎初始化失败是致命错误
            raise RuntimeError(error_msg) from e

    print(f"✅ 所有 {len(rov_physics_engines)} 个 ROV 物理引擎初始化完成")

    # 创建所有 ROV
    rov_prims = []
    rov_last_positions = []

    print("\n🤖 创建ROV系统:")
    for i, rov_config in enumerate(rov_configs):
        try:
            prim = create_rov_prim(stage, rov_config)
            rov_prims.append(prim)
            rov_last_positions.append([0, 0, rov_config["target_depth"]])

            color_emoji = "🔴" if "Original" in rov_config["name"] else "🔵" if "Main" in rov_config["name"] else "🟢"
            print(f"  {color_emoji} {rov_config['name']}: 质量={rov_config['mass']}kg, 目标深度={rov_config['target_depth']}m (统一物理引擎)")
        except Exception as e:
            print(f"❌ 创建 {rov_config['name']} 失败: {e}")
            return False

    # 强制加载物理
    try:
        physx_interface.force_load_physics_from_usd()
        print("✅ 物理属性已加载")
    except Exception as e:
        print(f"⚠️ 物理加载警告: {e}")

    # 启动时间轴
    try:
        sim_context._timeline.play()
        print("✅ 仿真时间轴已启动")
    except Exception as e:
        print(f"⚠️ 时间轴启动警告: {e}")

    # 仿真参数
    simulation_time = 0.0
    dt = 1.0 / 60.0  # 60 FPS
    max_simulation_time = 60.0  # 运行 60 秒
    frame_count = 0
    last_report_time = 0.0
    report_interval = 5.0  # 每 5 秒报告一次

    print(f"\n🎯 仿真目标:")
    for rov_config in rov_configs:
        color_emoji = "🔴" if "Original" in rov_config["name"] else "🔵" if "Main" in rov_config["name"] else "🟢"
        print(f"  {color_emoji} {rov_config['name']}: 目标深度 {rov_config['target_depth']}m")

    print(f"\n🔄 开始仿真循环 (最大 {max_simulation_time} 秒)...")
    print("💡 按 Ctrl+C 可以提前停止仿真")

    # 主仿真循环
    try:
        while simulation_time < max_simulation_time:
            frame_count += 1

            # 更新物理仿真
            try:
                omni.physx.acquire_physx_interface().update_simulation(
                    elapsedStep=dt,
                    currentTime=simulation_time
                )
            except Exception as e:
                if frame_count % 300 == 0:  # 每5秒报告一次错误
                    print(f"⚠️ 物理更新警告: {e}")

            # 更新每个 ROV 的物理
            for i, (rov_config, prim) in enumerate(zip(rov_configs, rov_prims)):
                if prim and prim.IsValid():
                    try:
                        # 获取当前位置
                        current_position = get_prim_position(prim)
                        last_position = rov_last_positions[i]

                        # 计算物理力（使用新的统一物理模块）
                        physics_result = calculate_rov_physics(
                            rov_config, current_position, last_position, dt, i
                        )

                        # 应用力
                        apply_force_to_rov(prim, physics_result['total_force'])

                        # 更新记录的位置
                        rov_last_positions[i] = current_position

                        # 定期输出状态
                        if frame_count % 300 == 0:  # 每 5 秒
                            depth = physics_result['depth']
                            buoyancy = physics_result['buoyancy_force']
                            control = physics_result['control_force']
                            drag = physics_result['drag_force']

                            color_emoji = "🔴" if "Original" in rov_config["name"] else "🔵" if "Main" in rov_config["name"] else "🟢"

                            # 显示统一物理模块的详细信息
                            if 'detailed_result' in physics_result:
                                detailed = physics_result['detailed_result']
                                buoyancy_info = detailed.get('buoyancy', {})
                                damping_info = detailed.get('damping', {})
                                control_info = detailed.get('control', {})

                                print(f"  {color_emoji} {rov_config['name']}: 深度={depth:.2f}m, "
                                      f"浮力={buoyancy:.0f}N, 控制={control:.0f}N, 阻力={drag:.0f}N")

                                # 显示更详细的物理信息
                                if buoyancy_info and damping_info:
                                    submerged_ratio = buoyancy_info.get('submerged_ratio', 0)
                                    total_damping = damping_info.get('total_damping', 0)
                                    print(f"    📊 浸没比例={submerged_ratio:.2f}, "
                                          f"阻尼系数={total_damping:.2f}")

                                # 显示控制信息
                                if control_info:
                                    depth_error = control_info.get('depth_error', 0)
                                    control_output = control_info.get('control_output', 0)
                                    print(f"    🎯 深度误差={depth_error:.3f}m, "
                                          f"控制输出={control_output:.1f}N")
                            else:
                                # 基本信息显示（如果详细结果不可用）
                                print(f"  {color_emoji} {rov_config['name']}: 深度={depth:.2f}m, "
                                      f"浮力={buoyancy:.0f}N, 控制={control:.0f}N, 阻力={drag:.0f}N")
                    except Exception as e:
                        if frame_count % 600 == 0:  # 每10秒报告一次ROV错误
                            print(f"⚠️ ROV {rov_config['name']} 更新警告: {e}")

            # 环境效果
            if frame_count % 600 == 0:  # 每 10 秒
                wave_height = 0.4 * math.sin(simulation_time * 0.4)
                current_x = 0.3 * math.cos(simulation_time * 0.08)
                current_y = 0.3 * math.sin(simulation_time * 0.08)
                print(f"🌊 环境: 波高={wave_height:.2f}m, 洋流=[{current_x:.2f}, {current_y:.2f}]m/s")

            # 系统状态报告
            if simulation_time - last_report_time >= report_interval:
                fps = frame_count / simulation_time if simulation_time > 0 else 60
                print(f"\n=== ROV系统状态 (t={simulation_time:.1f}s, fps={fps:.1f}) ===")
                print(f"✅ 独立模式 {len(rov_configs)}-ROV 水下物理系统运行中")
                print("� 使用统一物理计算模块")
                for rov_config in rov_configs:
                    color_emoji = "🔴" if "Original" in rov_config["name"] else "🔵" if "Main" in rov_config["name"] else "🟢"
                    print(f"{color_emoji} {rov_config['name']}: 质量={rov_config['mass']}kg, 目标深度={rov_config['target_depth']}m")
                last_report_time = simulation_time

            # 更新应用程序
            simulation_app.update()

            # 增加仿真时间
            simulation_time += dt

    except KeyboardInterrupt:
        print(f"\n🛑 用户中断仿真 (运行时间: {simulation_time:.1f}s)")
    except Exception as e:
        print(f"\n❌ 仿真循环错误: {e}")
        import traceback
        traceback.print_exc()

    print(f"\n🏁 仿真完成 - 总时间: {simulation_time:.1f}s, 总帧数: {frame_count}")

    # 停止仿真
    try:
        sim_context._timeline.stop()
        print("✅ 仿真时间轴已停止")
    except Exception as e:
        print(f"⚠️ 停止时间轴警告: {e}")

    return True


def initialize_isaac_sim():
    """初始化 Isaac Sim 核心组件"""
    global stage, sim_context, physx_interface

    print("🔧 初始化 Isaac Sim 核心组件...")

    # 初始化 Isaac Sim 核心组件
    stage = simulation_app.context.get_stage()
    sim_context = SimulationContext(stage_units_in_meters=1.0)

    # 获取 PhysX 接口
    physx_interface = omni.physx.acquire_physx_interface()

    print("✅ Isaac Sim 核心组件初始化完成")
    return True


def main():
    """主函数 - 独立模式入口"""
    print("=" * 70)
    print("🤖 独立模式多 ROV 水下系统")
    print("🏗️ 基于官方 Isaac Sim standalone 模式最佳实践")
    print("=" * 70)

    # 步骤1: 初始化 Isaac Sim 核心组件
    print("\n📋 初始化步骤:")
    print("1. ✅ SimulationApp 已启动")

    try:
        print("2. 🔄 初始化核心组件...")
        initialize_isaac_sim()
        print("2. ✅ 核心组件初始化完成")
    except Exception as e:
        print(f"❌ 核心组件初始化失败: {e}")
        return False

    # 步骤2: 加载环境
    try:
        print("3. 🔄 加载 USD 环境...")
        load_usd_environment()
        print("3. ✅ 环境加载完成")
    except Exception as e:
        print(f"❌ 环境加载失败: {e}")
        return False

    print("\n🤖 ROV 舰队配置:")
    for rov_config in rov_configs:
        color_emoji = "🔴" if "Original" in rov_config["name"] else "🔵" if "Main" in rov_config["name"] else "🟢"
        print(f"{color_emoji} {rov_config['name']}: {rov_config['size']}m 立方体, {rov_config['mass']}kg, 目标深度 {rov_config['target_depth']}m")

    print("\n🌊 功能特性:")
    print("✅ 每个 ROV 独立物理仿真")
    print("✅ 统一物理计算模块（必需）")
    print("✅ 真实的浮力、阻力和控制力计算")
    print("✅ 高精度 PID 控制器")
    print("✅ 推进器控制系统，支持 6DOF 运动")
    print("✅ 环境效果: 波浪、洋流、阻尼")
    print("✅ 多 ROV 监控和详细日志")
    print("✅ 独立模式，无需 ActionGraph")
    print("✅ 基于官方 Isaac Sim standalone 架构")
    print("✅ 模块化设计，易于扩展和复用")
    print("🚫 已移除简化物理计算（仅使用统一模块）")

    print("\n🎯 预期行为:")
    print("• ROV_Original (红色): 轻型，快速响应，浅水深度 (-1.5m)")

    # 步骤3: 运行仿真
    print("\n🚀 当前模式：完整多ROV物理仿真")
    print("🔧 ROV创建 + 物理仿真 + 浮力计算")

    try:
        success = run_multi_rov_simulation()
        if success:
            print("✅ 多ROV仿真完成")
        else:
            print("❌ 多ROV仿真失败")
    except Exception as e:
        print(f"❌ 仿真错误: {e}")
        import traceback
        traceback.print_exc()

    return True


if __name__ == "__main__":
    try:
        result = main()
        print(f"\n🏁 程序执行完成: {result}")
    except KeyboardInterrupt:
        print("\n🛑 用户中断仿真")
    except Exception as e:
        print(f"❌ 程序错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # ⚠️ 重要：关闭仿真应用程序
        print("\n🔄 关闭 Isaac Sim...")
        simulation_app.close()
