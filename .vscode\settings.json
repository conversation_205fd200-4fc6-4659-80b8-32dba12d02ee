{
    "editor.rulers": [120],
    "typescript.tsc.autoDetect": "off",
    "grunt.autoDetect": "off",
    "jake.autoDetect": "off",
    "gulp.autoDetect": "off",
    "npm.autoDetect": "off",
    "spellright.language": [
        "en"
    ],
    "spellright.documentTypes": [
        "markdown",
        "latex",
        "plaintext",
        "cpp",
        "asciidoc"
    ],

    // This enables python language server. Seems to work slightly better than jedi:
    "python.jediEnabled": false,

    // Those paths are automatically filled by build system, see `repo.toml` for configuration:
    "python.analysis.extraPaths": [
        "exts/isaacsim.app.about",
        "exts/isaacsim.app.selector",
        "exts/isaacsim.app.setup",
        "exts/isaacsim.asset.browser",
        "exts/isaacsim.asset.exporter.urdf",
        "exts/isaacsim.asset.exporter.urdf/pip_prebundle",
        "exts/isaacsim.asset.gen.conveyor",
        "exts/isaacsim.asset.gen.conveyor.ui",
        "exts/isaacsim.asset.gen.omap",
        "exts/isaacsim.asset.gen.omap.ui",
        "exts/isaacsim.asset.importer.heightmap",
        "exts/isaacsim.benchmark.examples",
        "exts/isaacsim.benchmark.services",
        "exts/isaacsim.code_editor.jupyter",
        "exts/isaacsim.code_editor.vscode",
        "exts/isaacsim.core.api",
        "exts/isaacsim.core.cloner",
        "exts/isaacsim.core.deprecation_manager",
        "exts/isaacsim.core.includes",
        "exts/isaacsim.core.nodes",
        "exts/isaacsim.core.prims",
        "exts/isaacsim.core.simulation_manager",
        "exts/isaacsim.core.throttling",
        "exts/isaacsim.core.utils",
        "exts/isaacsim.core.version",
        "exts/isaacsim.cortex.behaviors",
        "exts/isaacsim.cortex.framework",
        "exts/isaacsim.examples.browser",
        "exts/isaacsim.examples.extension",
        "exts/isaacsim.examples.interactive",
        "exts/isaacsim.examples.ui",
        "exts/isaacsim.gui.components",
        "exts/isaacsim.gui.menu",
        "exts/isaacsim.gui.property",
        "exts/isaacsim.replicator.behavior",
        "exts/isaacsim.replicator.behavior.ui",
        "exts/isaacsim.replicator.domain_randomization",
        "exts/isaacsim.replicator.examples",
        "exts/isaacsim.replicator.scene_blox",
        "exts/isaacsim.replicator.synthetic_recorder",
        "exts/isaacsim.replicator.writers",
        "exts/isaacsim.robot.manipulators",
        "exts/isaacsim.robot.manipulators.examples",
        "exts/isaacsim.robot.manipulators.ui",
        "exts/isaacsim.robot.policy.examples",
        "exts/isaacsim.robot.schema",
        "exts/isaacsim.robot.surface_gripper",
        "exts/isaacsim.robot.surface_gripper.ui",
        "exts/isaacsim.robot.wheeled_robots",
        "exts/isaacsim.robot.wheeled_robots.ui",
        "exts/isaacsim.robot_motion.lula",
        "exts/isaacsim.robot_motion.lula/pip_prebundle",
        "exts/isaacsim.robot_motion.lula_test_widget",
        "exts/isaacsim.robot_motion.motion_generation",
        "exts/isaacsim.robot_setup.assembler",
        "exts/isaacsim.robot_setup.gain_tuner",
        "exts/isaacsim.robot_setup.grasp_editor",
        "exts/isaacsim.robot_setup.import_wizard",
        "exts/isaacsim.robot_setup.xrdf_editor",
        "exts/isaacsim.ros1.bridge",
        "exts/isaacsim.ros2.bridge",
        "exts/isaacsim.ros2.tf_viewer",
        "exts/isaacsim.ros2.urdf",
        "exts/isaacsim.sensors.camera",
        "exts/isaacsim.sensors.camera.ui",
        "exts/isaacsim.sensors.physics",
        "exts/isaacsim.sensors.physics.examples",
        "exts/isaacsim.sensors.physics.ui",
        "exts/isaacsim.sensors.physx",
        "exts/isaacsim.sensors.physx.examples",
        "exts/isaacsim.sensors.physx.ui",
        "exts/isaacsim.sensors.rtx",
        "exts/isaacsim.sensors.rtx.ui",
        "exts/isaacsim.simulation_app",
        "exts/isaacsim.storage.native",
        "exts/isaacsim.test.collection",
        "exts/isaacsim.test.docstring",
        "exts/isaacsim.util.camera_inspector",
        "exts/isaacsim.util.clash_detection",
        "exts/isaacsim.util.debug_draw",
        "exts/isaacsim.util.internal",
        "exts/isaacsim.util.merge_mesh",
        "exts/isaacsim.util.physics",
        "exts/isaacsim.xr.openxr",
        "exts/omni.isaac.core_archive",
        "exts/omni.isaac.core_archive/pip_prebundle",
        "exts/omni.isaac.ml_archive",
        "exts/omni.isaac.ml_archive/pip_prebundle",
        "exts/omni.kit.loop-isaac",
        "exts/omni.kit.widget.cache_indicator",
        "exts/omni.pip.cloud",
        "exts/omni.pip.cloud/pip_prebundle",
        "exts/omni.pip.compute",
        "exts/omni.pip.compute/pip_prebundle",
        "extsDeprecated/omni.exporter.urdf",
        "extsDeprecated/omni.isaac.app.selector",
        "extsDeprecated/omni.isaac.app.setup",
        "extsDeprecated/omni.isaac.articulation_inspector",
        "extsDeprecated/omni.isaac.asset_browser",
        "extsDeprecated/omni.isaac.assets_check",
        "extsDeprecated/omni.isaac.benchmark.services",
        "extsDeprecated/omni.isaac.benchmarks",
        "extsDeprecated/omni.isaac.block_world",
        "extsDeprecated/omni.isaac.camera_inspector",
        "extsDeprecated/omni.isaac.cloner",
        "extsDeprecated/omni.isaac.common_includes",
        "extsDeprecated/omni.isaac.conveyor",
        "extsDeprecated/omni.isaac.conveyor.ui",
        "extsDeprecated/omni.isaac.core",
        "extsDeprecated/omni.isaac.core_nodes",
        "extsDeprecated/omni.isaac.cortex",
        "extsDeprecated/omni.isaac.cortex.sample_behaviors",
        "extsDeprecated/omni.isaac.debug_draw",
        "extsDeprecated/omni.isaac.doctest",
        "extsDeprecated/omni.isaac.dynamic_control",
        "extsDeprecated/omni.isaac.examples",
        "extsDeprecated/omni.isaac.examples_nodes",
        "extsDeprecated/omni.isaac.extension_templates",
        "extsDeprecated/omni.isaac.franka",
        "extsDeprecated/omni.isaac.gain_tuner",
        "extsDeprecated/omni.isaac.grasp_editor",
        "extsDeprecated/omni.isaac.import_wizard",
        "extsDeprecated/omni.isaac.jupyter_notebook",
        "extsDeprecated/omni.isaac.kit",
        "extsDeprecated/omni.isaac.lula",
        "extsDeprecated/omni.isaac.lula_test_widget",
        "extsDeprecated/omni.isaac.manipulators",
        "extsDeprecated/omni.isaac.manipulators.ui",
        "extsDeprecated/omni.isaac.menu",
        "extsDeprecated/omni.isaac.merge_mesh",
        "extsDeprecated/omni.isaac.motion_generation",
        "extsDeprecated/omni.isaac.nucleus",
        "extsDeprecated/omni.isaac.occupancy_map",
        "extsDeprecated/omni.isaac.occupancy_map.ui",
        "extsDeprecated/omni.isaac.physics_inspector",
        "extsDeprecated/omni.isaac.physics_utilities",
        "extsDeprecated/omni.isaac.proximity_sensor",
        "extsDeprecated/omni.isaac.quadruped",
        "extsDeprecated/omni.isaac.range_sensor",
        "extsDeprecated/omni.isaac.range_sensor.examples",
        "extsDeprecated/omni.isaac.range_sensor.ui",
        "extsDeprecated/omni.isaac.repl",
        "extsDeprecated/omni.isaac.robot_assembler",
        "extsDeprecated/omni.isaac.robot_description_editor",
        "extsDeprecated/omni.isaac.ros2_bridge",
        "extsDeprecated/omni.isaac.ros2_bridge.robot_description",
        "extsDeprecated/omni.isaac.ros_bridge",
        "extsDeprecated/omni.isaac.scene_blox",
        "extsDeprecated/omni.isaac.sensor",
        "extsDeprecated/omni.isaac.surface_gripper",
        "extsDeprecated/omni.isaac.surface_gripper.ui",
        "extsDeprecated/omni.isaac.synthetic_recorder",
        "extsDeprecated/omni.isaac.tests",
        "extsDeprecated/omni.isaac.tf_viewer",
        "extsDeprecated/omni.isaac.throttling",
        "extsDeprecated/omni.isaac.ui",
        "extsDeprecated/omni.isaac.ui_template",
        "extsDeprecated/omni.isaac.universal_robots",
        "extsDeprecated/omni.isaac.utils",
        "extsDeprecated/omni.isaac.version",
        "extsDeprecated/omni.isaac.vscode",
        "extsDeprecated/omni.isaac.wheeled_robots",
        "extsDeprecated/omni.isaac.wheeled_robots.ui",
        "extsDeprecated/omni.isaac.window.about",
        "extsDeprecated/omni.kit.property.isaac",
        "extsDeprecated/omni.replicator.isaac",
        "extsDeprecated/omni.usd.schema.isaac",
        "extsPhysics/omni.convexdecomposition",
        "extsPhysics/omni.kit.property.physx",
        "extsPhysics/omni.kvdb",
        "extsPhysics/omni.localcache",
        "extsPhysics/omni.physics.tensors",
        "extsPhysics/omni.physics.tensors.tests",
        "extsPhysics/omni.physx",
        "extsPhysics/omni.physx.bundle",
        "extsPhysics/omni.physx.camera",
        "extsPhysics/omni.physx.cct",
        "extsPhysics/omni.physx.commands",
        "extsPhysics/omni.physx.cooking",
        "extsPhysics/omni.physx.demos",
        "extsPhysics/omni.physx.fabric",
        "extsPhysics/omni.physx.forcefields",
        "extsPhysics/omni.physx.foundation",
        "extsPhysics/omni.physx.graph",
        "extsPhysics/omni.physx.internal",
        "extsPhysics/omni.physx.pvd",
        "extsPhysics/omni.physx.stageupdate",
        "extsPhysics/omni.physx.supportui",
        "extsPhysics/omni.physx.telemetry",
        "extsPhysics/omni.physx.tensors",
        "extsPhysics/omni.physx.tests",
        "extsPhysics/omni.physx.tests.mini",
        "extsPhysics/omni.physx.tests.visual",
        "extsPhysics/omni.physx.ui",
        "extsPhysics/omni.physx.vehicle",
        "extsPhysics/omni.physx.vehicle.tests",
        "extsPhysics/omni.physx.zerogravity",
        "extsPhysics/omni.usd.schema.forcefield",
        "extsPhysics/omni.usd.schema.physx",
        "extsPhysics/omni.usdphysics",
        "extsPhysics/omni.usdphysics.tests",
        "extsPhysics/omni.usdphysics.ui",
        "extscache/carb.audio-0.1.0+d02c707b.lx64.r.cp310",
        "extscache/carb.imaging.python-0.1.0+d02c707b.lx64.r.cp310",
        "extscache/carb.windowing.plugins-1.0.0+d02c707b.lx64.r",
        "extscache/isaacsim.asset.importer.mjcf-2.3.3+106.3.0.lx64.r.cp310",
        "extscache/isaacsim.asset.importer.urdf-2.3.10+106.4.0.lx64.r.cp310",
        "extscache/isaacsim.replicator.agent.camera_calibration-0.5.8+106.5.0",
        "extscache/isaacsim.replicator.agent.core-0.5.14+106.5.0",
        "extscache/isaacsim.replicator.agent.ui-0.5.13+106.5.0",
        "extscache/isaacsim.replicator.metropolis.utils-0.0.6+106.5.0",
        "extscache/isaacsim.replicator.object-0.3.21+106.5.0.lx64",
        "extscache/omni.activity.core-1.0.1+d02c707b.lx64.r.cp310",
        "extscache/omni.activity.profiler-1.0.4+d02c707b.lx64.r.cp310",
        "extscache/omni.activity.pump-1.0.0+d02c707b.lx64.r.cp310",
        "extscache/omni.activity.ui-1.1.1+d02c707b.lx64.r.cp310",
        "extscache/omni.activity.usd_resolver-1.0.3+d02c707b.lx64.r.cp310",
        "extscache/omni.anim.asset-106.5.1+106.5.0.lx64.r",
        "extscache/omni.anim.behavior.schema-106.5.0+106.5.0.lx64.r.cp310",
        "extscache/omni.anim.curve.bundle-1.2.3+106.4.0.ub3f",
        "extscache/omni.anim.curve.core-1.2.0+106.4.0.lx64.r.cp310.ub3f",
        "extscache/omni.anim.curve.ui-1.4.0+106.4.0.ub3f",
        "extscache/omni.anim.curve_editor-106.4.0+106.4.0.ub3f",
        "extscache/omni.anim.graph.bundle-106.5.0+106.5.0",
        "extscache/omni.anim.graph.core-106.5.1+106.5.0.lx64.r.cp310",
        "extscache/omni.anim.graph.schema-106.5.0+106.5.0.lx64.r.cp310",
        "extscache/omni.anim.graph.ui-106.5.0+106.5.0",
        "extscache/omni.anim.navigation.core-106.4.0+106.4.0.lx64.r.cp310",
        "extscache/omni.anim.navigation.recast-106.4.0+106.4.0.lx64.r.cp310",
        "extscache/omni.anim.navigation.schema-106.4.0+106.4.0.lx64.r.cp310",
        "extscache/omni.anim.navigation.ui-106.4.0+106.4.0",
        "extscache/omni.anim.people-0.6.7+106.5.0",
        "extscache/omni.anim.retarget.bundle-106.5.0+106.5.0",
        "extscache/omni.anim.retarget.core-106.5.1+106.5.0.lx64.r.cp310",
        "extscache/omni.anim.retarget.preview-106.5.3+106.5.0",
        "extscache/omni.anim.retarget.ui-106.5.2+106.5.0",
        "extscache/omni.anim.shared.core-106.0.2+106.2.0.lx64.r.cp310",
        "extscache/omni.anim.skelJoint-106.5.1+106.5.0.lx64.r.cp310",
        "extscache/omni.anim.timeline-106.4.2+106.4.0.lx64.r.cp310.ub3f",
        "extscache/omni.anim.widget.timeline-0.1.12+106.4.0.ub3f",
        "extscache/omni.anim.window.timeline-106.4.0+106.4.0.ub3f",
        "extscache/omni.appwindow-1.1.9+d02c707b.lx64.r.cp310",
        "extscache/omni.asset_validator.core-0.16.2",
        "extscache/omni.asset_validator.ui-0.16.2",
        "extscache/omni.assets.plugins-0.0.0+d02c707b.lx64.r",
        "extscache/omni.blobkey-1.1.2+d02c707b.lx64.r",
        "extscache/omni.client-1.2.2+d02c707b.lx64.r",
        "extscache/omni.command.usd-1.0.3+d02c707b",
        "extscache/omni.cuopt.examples-1.2.0+106.4.0",
        "extscache/omni.cuopt.service-1.2.0+106.4.0",
        "extscache/omni.cuopt.visualization-1.2.0+106.4.0",
        "extscache/omni.curve.creator-105.0.5+106.4.0.lx64.r.cp310",
        "extscache/omni.curve.manipulator-105.2.9+106.4.0.lx64.r.cp310",
        "extscache/omni.datastore-0.0.0+d02c707b.lx64.r",
        "extscache/omni.debugdraw-0.1.3+d02c707b.lx64.r.cp310",
        "extscache/omni.fabric.commands-1.1.5+d02c707b.lx64.r.cp310",
        "extscache/omni.genproc.core-106.1.0+106.4.0.lx64.r.cp310",
        "extscache/omni.gpu_foundation-0.0.0+d02c707b.lx64.r.cp310",
        "extscache/omni.gpu_foundation.shadercache.vulkan-1.0.0+d02c707b.lx64.r",
        "extscache/omni.gpucompute.plugins-0.0.0+d02c707b.lx64.r",
        "extscache/omni.graph-1.140.2+d02c707b.lx64.r.cp310",
        "extscache/omni.graph.action-1.120.0+106.5.0",
        "extscache/omni.graph.action_core-1.1.7+d02c707b.lx64.r.cp310",
        "extscache/omni.graph.action_nodes-1.40.1+106.5.0.lx64.r.cp310",
        "extscache/omni.graph.bundle.action-2.20.0+106.5.0",
        "extscache/omni.graph.core-2.181.8+d02c707b.lx64.r",
        "extscache/omni.graph.exec-0.9.4+d02c707b.lx64.r",
        "extscache/omni.graph.image.core-0.6.0+d02c707b.lx64.r",
        "extscache/omni.graph.image.nodes-1.3.0+d02c707b.lx64.r",
        "extscache/omni.graph.io-1.20.0+106.5.0",
        "extscache/omni.graph.nodes-1.162.1+106.5.0.lx64.r.cp310",
        "extscache/omni.graph.scriptnode-1.40.1+106.5.0",
        "extscache/omni.graph.telemetry-2.30.2+106.5.0.lx64.r.cp310",
        "extscache/omni.graph.tools-1.79.1+d02c707b",
        "extscache/omni.graph.tutorials-1.40.0+106.5.0.lx64.r.cp310",
        "extscache/omni.graph.ui-1.91.0+106.5.0.lx64.r.cp310",
        "extscache/omni.graph.ui_nodes-1.40.1+106.5.0.lx64.r.cp310",
        "extscache/omni.graph.visualization.nodes-2.1.1",
        "extscache/omni.graph.window.action-1.40.0+106.5.0",
        "extscache/omni.graph.window.core-1.130.1+106.5.0",
        "extscache/omni.graph.window.generic-1.40.0+106.5.0",
        "extscache/omni.hsscclient-1.1.1+d02c707b.lx64.r.cp310",
        "extscache/omni.hydra.engine.stats-1.0.3+d02c707b.lx64.r.cp310",
        "extscache/omni.hydra.iray.shadercache.vulkan-1.0.0+d02c707b.lx64.r",
        "extscache/omni.hydra.rtx-1.0.0+d02c707b.lx64.r",
        "extscache/omni.hydra.rtx.shadercache.vulkan-1.0.0+d02c707b.lx64.r",
        "extscache/omni.hydra.scene_api-0.1.2+d02c707b.lx64.r.cp310",
        "extscache/omni.hydra.scene_delegate-0.3.3+d02c707b.lx64.r",
        "extscache/omni.hydra.usdrt_delegate-7.5.1+d02c707b.lx64.r.cp310",
        "extscache/omni.importer.onshape-0.8.1+106.5.0",
        "extscache/omni.index-1.0.1+d02c707b.lx64.r",
        "extscache/omni.index.libs-380600.1777.0+d02c707b.lx64.r",
        "extscache/omni.inspect-1.0.1+d02c707b.lx64.r.cp310",
        "extscache/omni.iray.libs-0.0.0+d02c707b.lx64.r",
        "extscache/omni.kit.actions.core-1.0.0+d02c707b.lx64.r.cp310",
        "extscache/omni.kit.actions.window-1.1.1+d02c707b",
        "extscache/omni.kit.asset_converter-2.8.3+106.5.0.lx64.r.cp310",
        "extscache/omni.kit.audiodeviceenum-1.0.1+d02c707b.lx64.r.cp310",
        "extscache/omni.kit.browser.asset-1.3.11",
        "extscache/omni.kit.browser.core-2.3.11",
        "extscache/omni.kit.browser.folder.core-1.10.1",
        "extscache/omni.kit.browser.material-1.6.0",
        "extscache/omni.kit.browser.sample-1.4.8",
        "extscache/omni.kit.capture.viewport-1.5.3+d02c707b",
        "extscache/omni.kit.clipboard-1.0.5+d02c707b.lx64.r.cp310",
        "extscache/omni.kit.collaboration.channel_manager-1.0.12+d02c707b",
        "extscache/omni.kit.collaboration.presence_layer-1.0.9+d02c707b",
        "extscache/omni.kit.collaboration.telemetry-1.0.0+d02c707b.lx64.r.cp310",
        "extscache/omni.kit.commands-1.4.9+d02c707b.lx64.r.cp310",
        "extscache/omni.kit.context_menu-1.8.3+d02c707b",
        "extscache/omni.kit.converter.cad-202.2.0+106.5.0",
        "extscache/omni.kit.converter.common-503.2.1+106.5.0.lx64.r.cp310",
        "extscache/omni.kit.converter.dgn-503.3.1+106.5.0",
        "extscache/omni.kit.converter.dgn_core-503.3.2+106.5.0.lx64.r.cp310",
        "extscache/omni.kit.converter.hoops-504.4.3+106.5.0",
        "extscache/omni.kit.converter.hoops_core-504.4.1+106.5.0.lx64.r.cp310",
        "extscache/omni.kit.converter.jt-503.2.2+106.5.0",
        "extscache/omni.kit.converter.jt_core-503.2.2+106.5.0.lx64.r.cp310",
        "extscache/omni.kit.core.collection-0.2.0",
        "extscache/omni.kit.data2ui.core-1.0.27+106.0",
        "extscache/omni.kit.data2ui.usd-1.0.27+106.0",
        "extscache/omni.kit.debug.python-0.2.4+d02c707b.lx64.r.cp310",
        "extscache/omni.kit.debug.vscode-0.1.4+d02c707b",
        "extscache/omni.kit.environment.core-1.3.15",
        "extscache/omni.kit.exec.core-0.13.4+d02c707b.lx64.r.cp310",
        "extscache/omni.kit.gfn-107.0.2+106.2.0.lx64.r.cp310",
        "extscache/omni.kit.graph.delegate.default-1.2.2",
        "extscache/omni.kit.graph.delegate.modern-1.10.8",
        "extscache/omni.kit.graph.delegate.neo-1.1.3",
        "extscache/omni.kit.graph.editor.core-1.5.3",
        "extscache/omni.kit.graph.usd.commands-1.3.1",
        "extscache/omni.kit.graph.widget.variables-2.1.0",
        "extscache/omni.kit.helper.file_utils-0.1.9+d02c707b",
        "extscache/omni.kit.hotkeys.core-1.3.10+d02c707b",
        "extscache/omni.kit.hotkeys.window-1.4.8+d02c707b",
        "extscache/omni.kit.hydra_texture-1.4.0+d02c707b.lx64.r.cp310",
        "extscache/omni.kit.livestream.core-4.1.2+106.0.lx64.r.cp310",
        "extscache/omni.kit.livestream.core-6.1.0+106.2.0.lx64.r.cp310",
        "extscache/omni.kit.livestream.messaging-1.1.1",
        "extscache/omni.kit.livestream.native-5.0.1+106.2.0.lx64.r.cp310",
        "extscache/omni.kit.livestream.webrtc-6.0.0+106.2.0.lx64.r.cp310",
        "extscache/omni.kit.loop-isaac-1.2.0+106.5.0.lx64.r.cp310",
        "extscache/omni.kit.mainwindow-1.0.3+d02c707b",
        "extscache/omni.kit.manipulator.camera-106.0.3+d02c707b",
        "extscache/omni.kit.manipulator.prim-107.0.0+d02c707b",
        "extscache/omni.kit.manipulator.prim.core-107.0.7+d02c707b",
        "extscache/omni.kit.manipulator.prim.fabric-107.0.4+d02c707b",
        "extscache/omni.kit.manipulator.prim.usd-107.0.3+d02c707b",
        "extscache/omni.kit.manipulator.selection-106.0.1+d02c707b",
        "extscache/omni.kit.manipulator.selector-1.1.1+d02c707b",
        "extscache/omni.kit.manipulator.tool.mesh_snap-1.4.5+106.0.0",
        "extscache/omni.kit.manipulator.tool.snap-1.5.12+d02c707b",
        "extscache/omni.kit.manipulator.transform-106.0.1+d02c707b",
        "extscache/omni.kit.manipulator.viewport-107.0.1+d02c707b",
        "extscache/omni.kit.material.library-1.5.15+d02c707b",
        "extscache/omni.kit.menu.common-1.1.9+d02c707b",
        "extscache/omni.kit.menu.core-1.1.2+d02c707b",
        "extscache/omni.kit.menu.create-1.0.17+d02c707b",
        "extscache/omni.kit.menu.stage-1.2.5",
        "extscache/omni.kit.menu.utils-1.7.7+d02c707b",
        "extscache/omni.kit.mesh.raycast-105.4.0+106.0.0.lx64.r.cp310",
        "extscache/omni.kit.ngsearch-0.3.3",
        "extscache/omni.kit.notification_manager-1.0.9+d02c707b",
        "extscache/omni.kit.numpy.common-0.1.2+d02c707b.lx64.r.cp310",
        "extscache/omni.kit.pip_archive-0.0.0+d02c707b.lx64.cp310",
        "extscache/omni.kit.pip_archive-0.0.0+d02c707b.lx64.cp310/pip_prebundle",
        "extscache/omni.kit.pipapi-0.0.0+d02c707b",
        "extscache/omni.kit.playlist.core-1.3.4",
        "extscache/omni.kit.pointclouds-1.4.3+cp310",
        "extscache/omni.kit.preferences.animation-1.2.0+106.4.0.ub3f",
        "extscache/omni.kit.prim.icon-1.0.14",
        "extscache/omni.kit.primitive.mesh-1.0.17+d02c707b",
        "extscache/omni.kit.profiler.tracy-1.2.0+lx64",
        "extscache/omni.kit.profiler.window-2.3.1",
        "extscache/omni.kit.property.adapter.core-1.0.2+d02c707b",
        "extscache/omni.kit.property.adapter.fabric-1.0.3+d02c707b",
        "extscache/omni.kit.property.adapter.usd-1.0.2+d02c707b",
        "extscache/omni.kit.property.audio-1.0.16+d02c707b",
        "extscache/omni.kit.property.bundle-1.3.2+d02c707b",
        "extscache/omni.kit.property.camera-1.0.9+d02c707b",
        "extscache/omni.kit.property.collection-0.2.0",
        "extscache/omni.kit.property.environment-1.2.1",
        "extscache/omni.kit.property.geometry-1.3.3+d02c707b",
        "extscache/omni.kit.property.layer-1.1.8+d02c707b",
        "extscache/omni.kit.property.light-1.0.11+d02c707b",
        "extscache/omni.kit.property.material-1.10.17+d02c707b",
        "extscache/omni.kit.property.render-1.1.3+d02c707b",
        "extscache/omni.kit.property.sbsar-107.0.1",
        "extscache/omni.kit.property.transform-1.5.10+d02c707b",
        "extscache/omni.kit.property.usd-4.2.16+d02c707b",
        "extscache/omni.kit.quicklayout-1.0.8+d02c707b",
        "extscache/omni.kit.raycast.query-1.0.5+d02c707b.lx64.r.cp310",
        "extscache/omni.kit.renderer.capture-0.0.0+d02c707b.lx64.r.cp310",
        "extscache/omni.kit.renderer.core-1.0.2+d02c707b.lx64.r.cp310",
        "extscache/omni.kit.renderer.cuda_interop-1.0.1+d02c707b.lx64.r.cp310",
        "extscache/omni.kit.renderer.imgui-1.0.2+d02c707b.lx64.r.cp310",
        "extscache/omni.kit.renderer.init-0.0.0+d02c707b.lx64.r",
        "extscache/omni.kit.scripting-106.5.2+106.5.0",
        "extscache/omni.kit.search.files-1.0.4",
        "extscache/omni.kit.search.service-0.1.12",
        "extscache/omni.kit.search_core-1.0.6+d02c707b",
        "extscache/omni.kit.selection-0.1.5+d02c707b",
        "extscache/omni.kit.sequencer.core-103.4.2+106.5.0",
        "extscache/omni.kit.sequencer.usd-103.4.6+106.5.0",
        "extscache/omni.kit.stage.copypaste-1.2.2+d02c707b",
        "extscache/omni.kit.stage.mdl_converter-1.0.7+d02c707b",
        "extscache/omni.kit.stage_column.payload-2.0.0",
        "extscache/omni.kit.stage_column.variant-1.0.13",
        "extscache/omni.kit.stage_template.core-1.1.22+d02c707b",
        "extscache/omni.kit.stage_templates-1.2.6+d02c707b",
        "extscache/omni.kit.stagerecorder.bundle-105.0.2+106.4.0",
        "extscache/omni.kit.stagerecorder.core-105.0.5+106.4.0.lx64.r.cp310",
        "extscache/omni.kit.stagerecorder.ui-105.0.6+106.4.0",
        "extscache/omni.kit.streamsdk.plugins-4.1.1+106.0.lx64.r",
        "extscache/omni.kit.streamsdk.plugins-6.1.7+106.2.0.lx64.r",
        "extscache/omni.kit.telemetry-0.5.1+d02c707b.lx64.r.cp310",
        "extscache/omni.kit.test-1.1.2+d02c707b.lx64.r.cp310",
        "extscache/omni.kit.test_helpers_gfx-0.0.0+d02c707b",
        "extscache/omni.kit.test_suite.helpers-1.0.12+d02c707b",
        "extscache/omni.kit.thumbnails.mdl-1.0.24",
        "extscache/omni.kit.thumbnails.usd-1.0.9",
        "extscache/omni.kit.timeline.minibar-1.2.9",
        "extscache/omni.kit.tool.asset_exporter-1.6.2+106.5.0",
        "extscache/omni.kit.tool.asset_importer-2.12.2+106.5.0",
        "extscache/omni.kit.tool.collect-2.2.16+d02c707b",
        "extscache/omni.kit.tool.measure-105.2.7+106.3.0",
        "extscache/omni.kit.tool.remove_unused.controller-0.1.3",
        "extscache/omni.kit.tool.remove_unused.core-0.1.2",
        "extscache/omni.kit.ui.actions-1.0.2+d02c707b",
        "extscache/omni.kit.ui_test-1.3.3+d02c707b",
        "extscache/omni.kit.uiapp-0.0.0+d02c707b",
        "extscache/omni.kit.usd.collect-2.2.22+d02c707b",
        "extscache/omni.kit.usd.layers-2.2.0+d02c707b.lx64.r.cp310",
        "extscache/omni.kit.usd.mdl-1.0.7+d02c707b.lx64.r.cp310",
        "extscache/omni.kit.usd_undo-0.1.8+d02c707b",
        "extscache/omni.kit.usda_edit-1.1.14+d02c707b",
        "extscache/omni.kit.usdz_export-1.0.7+d02c707b",
        "extscache/omni.kit.variant.editor-106.1.1+106.1.0",
        "extscache/omni.kit.variant.presenter-107.0.0",
        "extscache/omni.kit.viewport.actions-107.0.0+d02c707b",
        "extscache/omni.kit.viewport.bundle-104.0.1+d02c707b",
        "extscache/omni.kit.viewport.legacy_gizmos-1.0.16+d02c707b.lx64.r.cp310",
        "extscache/omni.kit.viewport.manipulator.transform-107.0.4+d02c707b",
        "extscache/omni.kit.viewport.menubar.camera-107.0.2+d02c707b",
        "extscache/omni.kit.viewport.menubar.core-107.1.2+d02c707b",
        "extscache/omni.kit.viewport.menubar.display-107.0.3+d02c707b",
        "extscache/omni.kit.viewport.menubar.lighting-106.0.2+ub3f",
        "extscache/omni.kit.viewport.menubar.render-107.0.8+d02c707b",
        "extscache/omni.kit.viewport.menubar.settings-107.0.3+d02c707b",
        "extscache/omni.kit.viewport.menubar.waypoint-104.2.16",
        "extscache/omni.kit.viewport.registry-104.0.6+d02c707b",
        "extscache/omni.kit.viewport.rtx-104.0.1+d02c707b",
        "extscache/omni.kit.viewport.scene_camera_model-1.0.5+d02c707b.lx64.r.cp310",
        "extscache/omni.kit.viewport.utility-1.0.18+d02c707b",
        "extscache/omni.kit.viewport.window-107.0.8+d02c707b",
        "extscache/omni.kit.viewport_widgets_manager-1.0.8+d02c707b",
        "extscache/omni.kit.waypoint.bundle-1.0.4",
        "extscache/omni.kit.waypoint.core-1.4.57",
        "extscache/omni.kit.waypoint.playlist-1.0.8",
        "extscache/omni.kit.widget.browser_bar-2.0.10+d02c707b",
        "extscache/omni.kit.widget.calendar-1.0.8",
        "extscache/omni.kit.widget.collection-0.1.18",
        "extscache/omni.kit.widget.context_menu-1.2.4+d02c707b",
        "extscache/omni.kit.widget.extended_searchfield-1.0.29",
        "extscache/omni.kit.widget.filebrowser-2.10.52+d02c707b",
        "extscache/omni.kit.widget.filter-1.1.4+d02c707b",
        "extscache/omni.kit.widget.graph-1.12.17+d02c707b",
        "extscache/omni.kit.widget.highlight_label-1.0.3+d02c707b",
        "extscache/omni.kit.widget.imageview-1.0.3+d02c707b",
        "extscache/omni.kit.widget.layers-1.8.2+d02c707b",
        "extscache/omni.kit.widget.live-2.1.8+d02c707b",
        "extscache/omni.kit.widget.live_session_management-1.2.20+d02c707b",
        "extscache/omni.kit.widget.live_session_management.ui-1.0.1+d02c707b",
        "extscache/omni.kit.widget.material_preview-1.0.16",
        "extscache/omni.kit.widget.nucleus_connector-1.1.10+d02c707b",
        "extscache/omni.kit.widget.nucleus_info-1.0.2+d02c707b",
        "extscache/omni.kit.widget.options_button-1.0.3+d02c707b",
        "extscache/omni.kit.widget.options_menu-1.1.6+d02c707b",
        "extscache/omni.kit.widget.path_field-2.0.11+d02c707b",
        "extscache/omni.kit.widget.prompt-1.0.7+d02c707b",
        "extscache/omni.kit.widget.search_delegate-1.0.6+d02c707b",
        "extscache/omni.kit.widget.searchable_combobox-1.0.6+d02c707b",
        "extscache/omni.kit.widget.searchfield-1.1.8+d02c707b",
        "extscache/omni.kit.widget.settings-1.2.2+d02c707b",
        "extscache/omni.kit.widget.sliderbar-1.0.10",
        "extscache/omni.kit.widget.stage-2.11.6+d02c707b",
        "extscache/omni.kit.widget.stage_icons-1.0.6+d02c707b",
        "extscache/omni.kit.widget.text_editor-1.0.2+d02c707b.lx64.r.cp310",
        "extscache/omni.kit.widget.timeline-105.0.1+105.0",
        "extscache/omni.kit.widget.toolbar-1.7.3+d02c707b",
        "extscache/omni.kit.widget.versioning-1.4.9+d02c707b",
        "extscache/omni.kit.widget.viewport-107.0.7+d02c707b",
        "extscache/omni.kit.widget.zoombar-1.0.5",
        "extscache/omni.kit.widgets.custom-1.0.9",
        "extscache/omni.kit.window.collection-0.2.0",
        "extscache/omni.kit.window.commands-0.2.7+d02c707b",
        "extscache/omni.kit.window.console-0.2.14+d02c707b.lx64.r.cp310",
        "extscache/omni.kit.window.content_browser-2.10.3+d02c707b",
        "extscache/omni.kit.window.content_browser_registry-0.0.6+d02c707b",
        "extscache/omni.kit.window.cursor-1.1.4+d02c707b",
        "extscache/omni.kit.window.drop_support-1.0.4+d02c707b",
        "extscache/omni.kit.window.extensions-1.4.25+d02c707b",
        "extscache/omni.kit.window.file-1.3.57+d02c707b",
        "extscache/omni.kit.window.file_exporter-1.0.31+d02c707b",
        "extscache/omni.kit.window.file_importer-1.1.14+d02c707b",
        "extscache/omni.kit.window.filepicker-2.11.7+d02c707b",
        "extscache/omni.kit.window.material-1.6.2",
        "extscache/omni.kit.window.material_graph-1.8.19",
        "extscache/omni.kit.window.movie_capture-2.4.2",
        "extscache/omni.kit.window.popup_dialog-2.0.24+d02c707b",
        "extscache/omni.kit.window.preferences-1.7.0+d02c707b",
        "extscache/omni.kit.window.property-1.11.5+d02c707b",
        "extscache/omni.kit.window.quicksearch-2.4.4",
        "extscache/omni.kit.window.script_editor-1.7.8+d02c707b.lx64.r.cp310",
        "extscache/omni.kit.window.section-107.0.2",
        "extscache/omni.kit.window.stage-2.5.11+d02c707b",
        "extscache/omni.kit.window.stats-0.1.7+d02c707b",
        "extscache/omni.kit.window.status_bar-0.1.7+d02c707b.lx64.r.cp310",
        "extscache/omni.kit.window.title-1.1.5+d02c707b",
        "extscache/omni.kit.window.toolbar-1.6.2+d02c707b",
        "extscache/omni.kit.window.usd_paths-1.0.7+d02c707b",
        "extscache/omni.kit.window.usddebug-1.1.0",
        "extscache/omni.kit.xr.advertise-106.5.13+106.5.0",
        "extscache/omni.kit.xr.configurations-106.5.13+106.5.0",
        "extscache/omni.kit.xr.core-106.5.13+106.5.0.lx64.r.cp310",
        "extscache/omni.kit.xr.ogn-106.5.13+106.5.0",
        "extscache/omni.kit.xr.profile.ar-106.5.13+106.5.0",
        "extscache/omni.kit.xr.profile.common-106.5.13+106.5.0",
        "extscache/omni.kit.xr.profile.tabletar-106.5.13+106.5.0",
        "extscache/omni.kit.xr.profile.vr-106.5.13+106.5.0",
        "extscache/omni.kit.xr.scene_view.core-106.5.13+106.5.0.lx64.r.cp310",
        "extscache/omni.kit.xr.scene_view.utils-106.5.13+106.5.0",
        "extscache/omni.kit.xr.system.cloudxr-106.5.13+106.5.0.lx64.r.cp310",
        "extscache/omni.kit.xr.system.cloudxr41-106.5.13+106.5.0.lx64.r.cp310",
        "extscache/omni.kit.xr.system.openxr-106.5.13+106.5.0.lx64.r.cp310",
        "extscache/omni.kit.xr.system.simulatedxr-106.5.13+106.5.0.lx64.r.cp310",
        "extscache/omni.kit.xr.system.steamvr-106.5.13+106.5.0.lx64.r.cp310",
        "extscache/omni.kit.xr.telemetry-106.5.13+106.5.0.lx64.r.cp310",
        "extscache/omni.kit.xr.ui.stage-106.5.13+106.5.0",
        "extscache/omni.kit.xr.ui.window.profile-106.5.13+106.5.0",
        "extscache/omni.kit.xr.ui.window.viewport-106.5.13+106.5.0",
        "extscache/omni.materialx.libs-1.0.6+d02c707b.lx64.r.cp310",
        "extscache/omni.mdl-55.0.1+d02c707b.lx64.r.cp310",
        "extscache/omni.mdl.neuraylib-0.2.10+d02c707b.lx64.r.cp310",
        "extscache/omni.mdl.usd_converter-1.0.24+d02c707b",
        "extscache/omni.metrics.core-0.0.1+d02c707b.lx64.r",
        "extscache/omni.net-0.3.0-coreapi+lx64.r.cp310",
        "extscache/omni.no_code_ui.bundle-1.0.27+106.0",
        "extscache/omni.physx.clashdetection-106.5.0+106.5.0.lx64.r.cp310.ub3f",
        "extscache/omni.physx.clashdetection.core-106.5.0+106.5.0.cp310.ub3f",
        "extscache/omni.physx.clashdetection.telemetry-106.5.0+106.5.0.cp310.ub3f",
        "extscache/omni.ramp-105.1.16+106.4.0.lx64.r.cp310",
        "extscache/omni.replicator.core-1.11.35+106.5.0.lx64.r.cp310",
        "extscache/omni.replicator.replicator_yaml-2.0.10+lx64",
        "extscache/omni.resourcemonitor-107.0.0+d02c707b.lx64.r.cp310",
        "extscache/omni.rtx.settings.core-0.6.3+d02c707b",
        "extscache/omni.rtx.shadercache.vulkan-1.0.0+d02c707b.lx64.r",
        "extscache/omni.rtx.window.settings-0.6.17+d02c707b.lx64.r.cp310",
        "extscache/omni.scene.optimizer.bundle-106.1.10+106.1.0",
        "extscache/omni.scene.optimizer.core-106.1.10+106.1.0.lx64.r.cp310.ub3f",
        "extscache/omni.scene.optimizer.ui-106.1.10+106.1.0",
        "extscache/omni.scene.visualization.bundle-105.1.0+106.4.0",
        "extscache/omni.scene.visualization.core-105.4.14+106.4.0.lx64.r.cp310",
        "extscache/omni.scene.visualization.ui-105.1.2+106.4.0",
        "extscache/omni.sensors.nv.camera-0.20.1-coreapi+lx64.r",
        "extscache/omni.sensors.nv.common-2.5.0-coreapi+lx64.r.cp310",
        "extscache/omni.sensors.nv.ids-1.4.0-coreapi+lx64.r.cp310",
        "extscache/omni.sensors.nv.lidar-2.6.3-coreapi+lx64.r.cp310",
        "extscache/omni.sensors.nv.materials-1.4.0-coreapi+lx64.r.cp310",
        "extscache/omni.sensors.nv.radar-2.6.1-coreapi+lx64.r.cp310",
        "extscache/omni.sensors.nv.ultrasonic-2.4.0-coreapi+lx64.r.cp310",
        "extscache/omni.sensors.nv.wpm-2.4.0-coreapi+lx64.r",
        "extscache/omni.sensors.tiled-0.0.6+106.1.0.lx64.r",
        "extscache/omni.services.client-0.5.3",
        "extscache/omni.services.convert.cad-503.2.1+106.5.0",
        "extscache/omni.services.core-1.9.0",
        "extscache/omni.services.facilities.base-1.0.4",
        "extscache/omni.services.facilities.monitoring.progress-0.2.4",
        "extscache/omni.services.livestream.nvcf-6.2.0+106.2.0",
        "extscache/omni.services.pip_archive-0.13.6+lx64",
        "extscache/omni.services.starfleet.auth-0.1.5",
        "extscache/omni.services.transport.client.base-1.2.4",
        "extscache/omni.services.transport.client.http_async-1.3.6",
        "extscache/omni.services.transport.server.base-1.1.1",
        "extscache/omni.services.transport.server.http-1.3.1",
        "extscache/omni.services.transport.server.zeroconf-1.0.9",
        "extscache/omni.services.usd-1.1.0",
        "extscache/omni.simready.explorer-1.1.1",
        "extscache/omni.stats-1.0.1+d02c707b.lx64.r.cp310",
        "extscache/omni.syntheticdata-0.6.10+d02c707b.lx64.r.cp310",
        "extscache/omni.timeline-1.0.11+d02c707b.lx64.r.cp310",
        "extscache/omni.timeline.live_session-1.0.8+d02c707b",
        "extscache/omni.tools.array-105.0.4",
        "extscache/omni.ui-2.26.5+d02c707b.lx64.r.cp310",
        "extscache/omni.ui.scene-1.11.2+d02c707b.lx64.r.cp310",
        "extscache/omni.ui_query-1.1.7+d02c707b",
        "extscache/omni.uiaudio-1.0.0+d02c707b.lx64.r.cp310",
        "extscache/omni.ujitso.client-0.0.0+d02c707b.lx64.r",
        "extscache/omni.ujitso.default-1.0.0+d02c707b.lx64.r.cp310",
        "extscache/omni.ujitso.processor.texture-1.0.0+d02c707b.lx64.r",
        "extscache/omni.usd-1.12.4+d02c707b.lx64.r.cp310",
        "extscache/omni.usd.config-1.0.5+d02c707b",
        "extscache/omni.usd.core-1.4.2+d02c707b.lx64.r",
        "extscache/omni.usd.fileformat.e57-1.3.3+106.4.0.lx64.r.cp310.ub3f",
        "extscache/omni.usd.fileformat.pts-106.4.0+106.4.0.lx64.r.cp310.ub3f",
        "extscache/omni.usd.fileformat.sbsar-107.0.2+lx64.r.cp310.ub3f",
        "extscache/omni.usd.libs-1.0.1+d02c707b.lx64.r.cp310",
        "extscache/omni.usd.metrics.assembler-106.5.0+106.5.0.lx64.r.cp310",
        "extscache/omni.usd.metrics.assembler.physics-106.5.0+106.5.0",
        "extscache/omni.usd.metrics.assembler.ui-106.5.0+106.5.0",
        "extscache/omni.usd.schema.anim-0.0.0+d02c707b.lx64.r.cp310",
        "extscache/omni.usd.schema.audio-0.0.0+d02c707b.lx64.r.cp310",
        "extscache/omni.usd.schema.geospatial-0.0.0+d02c707b.lx64.r.cp310",
        "extscache/omni.usd.schema.metrics.assembler-106.5.0+106.5.0.lx64.r.cp310",
        "extscache/omni.usd.schema.omnigraph-1.0.0+d02c707b.lx64.r.cp310",
        "extscache/omni.usd.schema.omniscripting-1.0.0+d02c707b.lx64.r.cp310",
        "extscache/omni.usd.schema.physx.clashdetection-106.5.0+106.5.0.lx64.r.cp310",
        "extscache/omni.usd.schema.scene.visualization-2.0.2+106.4.0",
        "extscache/omni.usd.schema.semantics-0.0.0+d02c707b.lx64.r.cp310",
        "extscache/omni.usd.schema.sequence-2.3.1+106.5.0.lx64.r.cp310",
        "extscache/omni.usd_resolver-1.0.0+d02c707b.lx64.r.cp310",
        "extscache/omni.vdb_timesample_editor-0.1.11",
        "extscache/omni.videoencoding-0.1.1+d02c707b.lx64.r.cp310",
        "extscache/omni.volume-0.5.0+d02c707b.lx64.r.cp310",
        "extscache/omni.warehouse_creator-0.4.4",
        "extscache/omni.warp-1.5.0",
        "extscache/omni.warp.core-1.5.0+lx64",
        "extscache/semantics.schema.editor-0.3.10",
        "extscache/semantics.schema.property-1.0.5",
        "extscache/usdrt.scenegraph-7.5.1+d02c707b.lx64.r.cp310",
        "kit/extscore/omni.client.lib",
        "kit/extscore/omni.kit.async_engine",
        "kit/extscore/omni.kit.registry.nucleus",
        "kit/kernel/py",
        "kit/plugins/bindings-python",
        "kit/python/lib/python3.10",
        "kit/python/lib/python3.10/site-packages",
],

    "python.languageServer": "Pylance",
    "python.defaultInterpreterPath": "/home/<USER>/isaac-sim/kit/python/bin/python3",
    // python.pythonPath is deprecated
    // "python.pythonPath": "/home/<USER>/isaac-sim/kit/python/bin/python3",


    // This enables python language server. Seems to work slightly better than jedi:
    "python.jediEnabled": false, 

    // We use "black" as a formatter:
    "python.formatting.provider": "black",
    "python.formatting.blackArgs": ["--line-length", "120"],

    // Use flake8 for linting
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": true,
    "python.languageServer": "Pylance"
}
