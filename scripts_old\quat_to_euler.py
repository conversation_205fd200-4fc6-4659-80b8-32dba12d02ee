"""
四元数到欧拉角转换模块
提供将四元数表示的旋转转换为欧拉角的功能
用于水下机器人姿态表示的转换

重构版本：同时支持 ActionGraph 模式和纯 Python 调用
"""

import numpy as np

# ==================== 纯 Python 接口 ====================

def quaternion_to_euler(quaternion, degrees=True):
    """
    将四元数转换为欧拉角（纯 Python 版本）

    四元数是表示三维旋转的数学工具，具有无奇点、计算稳定等优点
    欧拉角是更直观的旋转表示方法，包含横滚角、俯仰角和偏航角

    参数:
        quaternion: 四元数 [x, y, z, w] 或 [w, x, y, z]
                   支持两种常见的四元数格式
        degrees: 是否返回度数，默认 True（度数），False 返回弧度

    返回:
        dict: 欧拉角转换结果
            {
                'roll': float,      # 横滚角，绕X轴旋转
                'pitch': float,     # 俯仰角，绕Y轴旋转
                'yaw': float,       # 偏航角，绕Z轴旋转
                'euler_angles': list, # [roll, pitch, yaw]
                'input_format': str   # 输入格式说明
            }
    """
    # 检测四元数格式并标准化为 [x, y, z, w] 格式
    if len(quaternion) != 4:
        raise ValueError("四元数必须包含4个分量")

    # 自动检测格式：通常最大分量是 w（标量部分）
    quat = np.array(quaternion)
    max_idx = np.argmax(np.abs(quat))

    if max_idx == 0:
        # 可能是 [w, x, y, z] 格式
        w, x, y, z = quat[0], quat[1], quat[2], quat[3]
        input_format = "[w, x, y, z] 格式"
    else:
        # 假设是 [x, y, z, w] 格式
        x, y, z, w = quat[0], quat[1], quat[2], quat[3]
        input_format = "[x, y, z, w] 格式"

    # 归一化四元数
    norm = np.sqrt(x*x + y*y + z*z + w*w)
    if norm > 0:
        x, y, z, w = x/norm, y/norm, z/norm, w/norm

    # 计算横滚角 (Roll) - 绕X轴旋转
    t0 = 2 * (w * x + y * z)
    t1 = 1 - 2 * (x * x + y * y)
    roll = np.arctan2(t0, t1)

    # 计算俯仰角 (Pitch) - 绕Y轴旋转
    t2 = 2 * (w * y - z * x)
    t2 = np.clip(t2, -1.0, 1.0)  # 避免 arcsin 域错误
    pitch = np.arcsin(t2)

    # 计算偏航角 (Yaw) - 绕Z轴旋转
    t3 = 2 * (w * z + x * y)
    t4 = 1 - 2 * (y * y + z * z)
    yaw = np.arctan2(t3, t4)

    # 转换为度数（如果需要）
    if degrees:
        roll = np.degrees(roll)
        pitch = np.degrees(pitch)
        yaw = np.degrees(yaw)

    return {
        'roll': roll,
        'pitch': pitch,
        'yaw': yaw,
        'euler_angles': [roll, pitch, yaw],
        'input_format': input_format
    }

def euler_to_quaternion(roll, pitch, yaw, degrees=True):
    """
    将欧拉角转换为四元数（纯 Python 版本）

    参数:
        roll: 横滚角，绕X轴旋转
        pitch: 俯仰角，绕Y轴旋转
        yaw: 偏航角，绕Z轴旋转
        degrees: 输入是否为度数，默认 True

    返回:
        dict: 四元数转换结果
            {
                'quaternion': list,     # [x, y, z, w] 格式
                'quaternion_wxyz': list, # [w, x, y, z] 格式
                'norm': float           # 四元数模长（应该为1）
            }
    """
    # 转换为弧度（如果需要）
    if degrees:
        roll = np.radians(roll)
        pitch = np.radians(pitch)
        yaw = np.radians(yaw)

    # 计算半角
    cr = np.cos(roll * 0.5)
    sr = np.sin(roll * 0.5)
    cp = np.cos(pitch * 0.5)
    sp = np.sin(pitch * 0.5)
    cy = np.cos(yaw * 0.5)
    sy = np.sin(yaw * 0.5)

    # 计算四元数分量
    w = cr * cp * cy + sr * sp * sy
    x = sr * cp * cy - cr * sp * sy
    y = cr * sp * cy + sr * cp * sy
    z = cr * cp * sy - sr * sp * cy

    # 计算模长
    norm = np.sqrt(x*x + y*y + z*z + w*w)

    return {
        'quaternion': [x, y, z, w],
        'quaternion_wxyz': [w, x, y, z],
        'norm': norm
    }

def validate_quaternion(quaternion):
    """
    验证四元数的有效性

    参数:
        quaternion: 四元数 [x, y, z, w] 或 [w, x, y, z]

    返回:
        dict: 验证结果
            {
                'is_valid': bool,       # 是否有效
                'norm': float,          # 模长
                'is_unit': bool,        # 是否为单位四元数
                'message': str          # 验证信息
            }
    """
    if len(quaternion) != 4:
        return {
            'is_valid': False,
            'norm': 0.0,
            'is_unit': False,
            'message': '四元数必须包含4个分量'
        }

    quat = np.array(quaternion)
    norm = np.sqrt(np.sum(quat**2))
    is_unit = abs(norm - 1.0) < 1e-6

    return {
        'is_valid': True,
        'norm': norm,
        'is_unit': is_unit,
        'message': f'四元数有效，模长={norm:.6f}，{"是" if is_unit else "不是"}单位四元数'
    }

# ==================== ActionGraph 兼容接口 ====================

def setup(db):
    """
    初始化函数（ActionGraph 兼容）
    设置必要的变量或数据结构

    参数:
        db: 数据库对象，用于存储和传递数据

    注意:
        此模块不需要特殊的初始化操作
    """
    pass  # 此情况下不需要初始化操作

def compute(db):
    """
    将四元数转换为欧拉角（ActionGraph 兼容版本）

    参数:
        db: 数据库对象，包含输入和输出数据
            输入参数:
                quaternion: 四元数 [x, y, z, w]
            输出参数:
                rotation: 欧拉角 [roll, pitch, yaw] (度)
    """
    # 获取输入四元数
    quaternion = db.inputs.quaternion

    # 使用纯 Python 函数计算
    result = quaternion_to_euler(quaternion, degrees=True)

    # 输出欧拉角数组
    db.outputs.rotation = result['euler_angles']

# ==================== 测试和演示函数 ====================

def test_quaternion_conversion():
    """测试四元数转换模块"""
    print("🧪 测试四元数转换模块 (quat_to_euler.py)")
    print("="*50)

    print("📖 模块功能说明:")
    print("   - 四元数到欧拉角的双向转换")
    print("   - 支持多种四元数格式 [x,y,z,w] 和 [w,x,y,z]")
    print("   - 自动格式检测和归一化")
    print("   - 避免万向锁和数值不稳定问题")
    print("   - 同时支持 ActionGraph 和纯 Python 调用")

    # 1. 测试标准旋转的四元数转换
    print(f"\n1️⃣ 测试标准旋转的四元数转换:")

    # 标准测试用例：已知的旋转
    test_cases = [
        {
            'name': '无旋转',
            'quat': [0, 0, 0, 1],  # [x, y, z, w]
            'expected': [0, 0, 0]
        },
        {
            'name': '绕X轴90度',
            'quat': [0.7071, 0, 0, 0.7071],
            'expected': [90, 0, 0]
        },
        {
            'name': '绕Y轴90度',
            'quat': [0, 0.7071, 0, 0.7071],
            'expected': [0, 90, 0]
        },
        {
            'name': '绕Z轴90度',
            'quat': [0, 0, 0.7071, 0.7071],
            'expected': [0, 0, 90]
        },
        {
            'name': '复合旋转',
            'quat': [0.1830, 0.3536, 0.1830, 0.9007],
            'expected': [30, 45, 30]  # 近似值
        }
    ]

    for case in test_cases:
        result = quaternion_to_euler(case['quat'])
        euler = result['euler_angles']

        print(f"   {case['name']}:")
        print(f"     输入四元数: {case['quat']}")
        print(f"     输出欧拉角: [{euler[0]:.1f}°, {euler[1]:.1f}°, {euler[2]:.1f}°]")
        print(f"     检测格式: {result['input_format']}")

        # 验证精度
        expected = case['expected']
        error = [abs(euler[i] - expected[i]) for i in range(3)]
        max_error = max(error)
        print(f"     最大误差: {max_error:.2f}°")

def test_bidirectional_conversion():
    """测试双向转换的一致性"""
    print(f"\n2️⃣ 测试双向转换的一致性:")

    # 测试欧拉角 -> 四元数 -> 欧拉角的往返转换
    test_euler_angles = [
        [30, 45, 60],
        [0, 90, 0],
        [45, 0, 45],
        [10, 20, 30],
        [90, 45, 180]
    ]

    for original_euler in test_euler_angles:
        # 欧拉角 -> 四元数
        quat_result = euler_to_quaternion(original_euler[0], original_euler[1], original_euler[2])
        quaternion = quat_result['quaternion']

        # 四元数 -> 欧拉角
        euler_result = quaternion_to_euler(quaternion)
        recovered_euler = euler_result['euler_angles']

        print(f"   原始欧拉角: [{original_euler[0]:.1f}°, {original_euler[1]:.1f}°, {original_euler[2]:.1f}°]")
        print(f"   中间四元数: [{quaternion[0]:.4f}, {quaternion[1]:.4f}, {quaternion[2]:.4f}, {quaternion[3]:.4f}]")
        print(f"   恢复欧拉角: [{recovered_euler[0]:.1f}°, {recovered_euler[1]:.1f}°, {recovered_euler[2]:.1f}°]")

        # 计算误差
        errors = [abs(original_euler[i] - recovered_euler[i]) for i in range(3)]
        # 处理角度周期性（例如 180° 和 -180° 是相同的）
        for i in range(3):
            if errors[i] > 180:
                errors[i] = 360 - errors[i]

        max_error = max(errors)
        print(f"   往返误差: {max_error:.3f}°")

def test_quaternion_validation():
    """测试四元数验证功能"""
    print(f"\n3️⃣ 测试四元数验证功能:")

    test_quaternions = [
        {'quat': [0, 0, 0, 1], 'desc': '标准单位四元数'},
        {'quat': [0.5, 0.5, 0.5, 0.5], 'desc': '归一化四元数'},
        {'quat': [1, 1, 1, 1], 'desc': '未归一化四元数'},
        {'quat': [0.1, 0.2, 0.3], 'desc': '无效四元数（3分量）'},
        {'quat': [0, 0, 0, 0], 'desc': '零四元数'}
    ]

    for test in test_quaternions:
        try:
            validation = validate_quaternion(test['quat'])
            print(f"   {test['desc']}:")
            print(f"     四元数: {test['quat']}")
            print(f"     验证结果: {validation['message']}")
        except Exception as e:
            print(f"   {test['desc']}: 验证失败 - {str(e)}")

def demo_usage_examples():
    """演示使用示例"""
    print(f"\n📚 使用示例:")
    print("="*30)

    print("\n💡 示例 1: 四元数转欧拉角")
    print("```python")
    print("from scripts.quat_to_euler import quaternion_to_euler")
    print("")
    print("# 转换四元数为欧拉角")
    print("result = quaternion_to_euler([0, 0, 0.7071, 0.7071])  # 绕Z轴90度")
    print("print(f'欧拉角: {result[\"euler_angles\"]}')")
    print("```")

    # 实际运行示例
    result = quaternion_to_euler([0, 0, 0.7071, 0.7071])
    print(f"➡️ 输出: 欧拉角: {[f'{angle:.1f}°' for angle in result['euler_angles']]}")

    print("\n💡 示例 2: 欧拉角转四元数")
    print("```python")
    print("from scripts.quat_to_euler import euler_to_quaternion")
    print("")
    print("# 转换欧拉角为四元数")
    print("quat_result = euler_to_quaternion(30, 45, 60)  # Roll, Pitch, Yaw")
    print("print(f'四元数: {quat_result[\"quaternion\"]}')")
    print("```")

    # 实际运行示例
    quat_result = euler_to_quaternion(30, 45, 60)
    quat = quat_result['quaternion']
    print(f"➡️ 输出: 四元数: [{quat[0]:.4f}, {quat[1]:.4f}, {quat[2]:.4f}, {quat[3]:.4f}]")

    print("\n💡 示例 3: 四元数验证")
    print("```python")
    print("from scripts.quat_to_euler import validate_quaternion")
    print("")
    print("validation = validate_quaternion([0, 0, 0, 1])")
    print("print(validation['message'])")
    print("```")

    # 实际运行示例
    validation = validate_quaternion([0, 0, 0, 1])
    print(f"➡️ 输出: {validation['message']}")

if __name__ == "__main__":
    """当直接运行此文件时执行测试和演示"""
    test_quaternion_conversion()
    test_bidirectional_conversion()
    test_quaternion_validation()
    demo_usage_examples()
    print("\n✅ 四元数转换模块测试完成！")
    print("\n🔗 相关模块:")
    print("   - buoyancy_forces.py: 旋转浮力计算")
    print("   - rov_physics_unified.py: 统一物理引擎")
    print("   - 用于 ROV 姿态控制和旋转计算")
