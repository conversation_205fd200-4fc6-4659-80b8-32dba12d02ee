# -*- coding: utf-8 -*-
"""
二维浅水波动仿真程序
使用有限差分法求解浅水方程组

创建时间: 2018年2月17日 11:26:20
作者: Chi

该程序实现了二维浅水波动的数值仿真，基于浅水方程组：
∂h/∂t + ∂(hu)/∂x + ∂(hv)/∂y = 0                    (连续性方程)
∂u/∂t + u∂u/∂x + v∂u/∂y + g∂h/∂x = 0              (x方向动量方程)
∂v/∂t + u∂v/∂x + v∂v/∂y + g∂h/∂y = 0              (y方向动量方程)

其中：
h - 水深 (m)
u - x方向流速 (m/s)
v - y方向流速 (m/s)
g - 重力加速度 (m/s²)
"""

# =============================================================================
# 导入必要的库和初始化参数
# =============================================================================

import numpy as np

# 仿真参数设置
n = 15      # 网格密度 (网格数量为n×n)
g = 9.8     # 重力加速度 (m/s²)
dt = 0.04   # 时间步长 (s) - 注释中的"running velocity"应为时间步长
dx = 1.0    # x方向网格间距 (m)
dy = 1.0    # y方向网格间距 (m)

# 主要物理量数组初始化 (包含边界，大小为(n+2)×(n+2))
h = np.ones((n+2,n+2))    # 水深场，初始化为1.0米
u = np.zeros((n+2,n+2))   # x方向速度场，初始化为0
v = np.zeros((n+2,n+2))   # y方向速度场，初始化为0

# x方向半步长点的物理量 (用于Lax-Wendroff格式)
hx = np.zeros((n+1,n+1))  # x方向半步长点的水深
ux = np.zeros((n+1,n+1))  # x方向半步长点的u速度
vx = np.zeros((n+1,n+1))  # x方向半步长点的v速度

# y方向半步长点的物理量 (用于Lax-Wendroff格式)
hy = np.zeros((n+1,n+1))  # y方向半步长点的水深
uy = np.zeros((n+1,n+1))  # y方向半步长点的u速度
vy = np.zeros((n+1,n+1))  # y方向半步长点的v速度

# 仿真控制参数
nsteps = 0      # 时间步数计数器
h[1,1] = 0.5    # 在(1,1)位置设置初始扰动，水深为0.5米（产生波源）

def reflective():
    """
    反射边界条件函数

    在计算域边界处施加反射边界条件：
    - 水深h：在边界处使用内部相邻点的值（零梯度条件）
    - 速度u,v：在相应边界处使用反射条件
      * 在垂直边界（左右）：u取相反值，v保持相同值
      * 在水平边界（上下）：v取相反值，u保持相同值

    这种边界条件模拟了波在固体壁面的反射现象
    """
    # 水深边界条件：零梯度（Neumann边界条件）
    h[:,0] = h[:,1]      # 左边界：h(0,j) = h(1,j)
    h[:,n+1] = h[:,n]    # 右边界：h(n+1,j) = h(n,j)
    h[0,:] = h[1,:]      # 下边界：h(i,0) = h(i,1)
    h[n+1,:] = h[n,:]    # 上边界：h(i,n+1) = h(i,n)

    # u速度边界条件
    u[:,0] = u[:,1]      # 左边界：u分量保持连续
    u[:,n+1] = u[:,n]    # 右边界：u分量保持连续
    u[0,:] = -u[1,:]     # 下边界：u分量反射（取相反值）
    u[n+1,:] = -u[n,:]   # 上边界：u分量反射（取相反值）

    # v速度边界条件
    v[:,0] = -v[:,1]     # 左边界：v分量反射（取相反值）
    v[:,n+1] = -v[:,n]   # 右边界：v分量反射（取相反值）
    v[0,:] = v[1,:]      # 下边界：v分量保持连续
    v[n+1,:] = v[n,:]    # 上边界：v分量保持连续

def proses():
    """
    主要的数值计算函数 - 使用Lax-Wendroff有限差分格式求解浅水方程组

    该函数实现了二步Lax-Wendroff格式：
    1. 第一步：计算半时间步长、半空间步长处的物理量
    2. 第二步：使用半步长处的值更新下一时间步的物理量

    求解的方程组：
    ∂h/∂t + ∂(hu)/∂x + ∂(hv)/∂y = 0
    ∂u/∂t + u∂u/∂x + v∂u/∂y + g∂h/∂x = 0
    ∂v/∂t + u∂v/∂x + v∂v/∂y + g∂h/∂y = 0

    返回:
        tuple: 更新后的(h, u, v)数组
    """

    # 第一步：计算x方向半步长点的物理量
    # 使用Lax-Wendroff格式的预测步
    for i in range(n+1):
        for j in range(n):
            # x方向半步长点的水深
            hx[i,j] = (h[i+1,j+1]+h[i,j+1])/2 - dt/(2*dx)*(u[i+1,j+1]-u[i,j+1])

            # x方向半步长点的u速度（包含压力梯度和对流项）
            ux[i,j] = (u[i+1,j+1]+u[i,j+1])/2 - dt/(2*dx)*((pow(u[i+1,j+1],2)/h[i+1,j+1] + g/2*pow(h[i+1,j+1],2)) - (pow(u[i,j+1],2)/h[i,j+1] + g/2*pow(h[i,j+1],2)))

            # x方向半步长点的v速度（对流项）
            vx[i,j] = (v[i+1,j+1]+v[i,j+1])/2 - dt/(2*dx)*((u[i+1,j+1]*v[i+1,j+1]/h[i+1,j+1]) - (u[i,j+1]*v[i,j+1]/h[i,j+1]))

    # 第一步：计算y方向半步长点的物理量
    for i in range(n):
        for j in range(n+1):
            # y方向半步长点的水深
            hy[i,j] = (h[i+1,j+1]+h[i+1,j])/2 - dt/(2*dy)*(v[i+1,j+1]-v[i+1,j])

            # y方向半步长点的u速度（对流项）
            uy[i,j] = (u[i+1,j+1]+u[i+1,j])/2 - dt/(2*dy)*((v[i+1,j+1]*u[i+1,j+1]/h[i+1,j+1]) - (v[i+1,j]*u[i+1,j]/h[i+1,j]))

            # y方向半步长点的v速度（包含压力梯度和对流项）
            vy[i,j] = (v[i+1,j+1]+v[i+1,j])/2 - dt/(2*dy)*((pow(v[i+1,j+1],2)/h[i+1,j+1] + g/2*pow(h[i+1,j+1],2)) - (pow(v[i+1,j],2)/h[i+1,j] + g/2*pow(h[i+1,j],2)))

    # 第二步：使用半步长处的值更新下一时间步的物理量（校正步）
    for i in range(1,n+1):
        for j in range(1,n+1):
            # 更新水深（连续性方程）
            h[i,j] = h[i,j] - (dt/dx)*(ux[i,j-1]-ux[i-1,j-1]) - (dt/dy)*(vy[i-1,j]-vy[i-1,j-1])

            # 更新u速度（x方向动量方程）
            u[i,j] = u[i,j] - (dt/dx)*((pow(ux[i,j-1],2)/hx[i,j-1] + g/2*pow(hx[i,j-1],2)) - (pow(ux[i-1,j-1],2)/hx[i-1,j-1] + g/2*pow(hx[i-1,j-1],2))) - (dt/dy)*((vy[i-1,j]*uy[i-1,j]/hy[i-1,j]) - (vy[i-1,j-1]*uy[i-1,j-1]/hy[i-1,j-1]))

            # 更新v速度（y方向动量方程）
            v[i,j] = v[i,j] - (dt/dx)*((ux[i,j-1]*vx[i,j-1]/hx[i,j-1]) - (ux[i-1,j-1]*vx[i-1,j-1]/hx[i-1,j-1])) - (dt/dy)*((pow(vy[i-1,j],2)/hy[i-1,j] + g/2*pow(hy[i-1,j],2)) - (pow(vy[i-1,j-1],2)/hy[i-1,j-1] + g/2*pow(hy[i-1,j-1],2)))

    # 应用边界条件
    reflective()
    return h,u,v
'''
for i in range (17):
    #print h
    proses(1)
'''

import matplotlib.pyplot as plt
from matplotlib import cm
from matplotlib.ticker import LinearLocator, FormatStrFormatter
from mpl_toolkits.mplot3d import Axes3D
a = n
x = np.arange(n+2)
y = np.arange(n+2)
x,y = np.meshgrid(x,y)

fig = plt.figure()

ax = fig.add_subplot(111, projection='3d')

def plotset():
    ax.set_xlim3d(0, a)
    ax.set_ylim3d(0, a)
    ax.set_zlim3d(0.5,1.5)
    ax.set_autoscalez_on(False)
    ax.zaxis.set_major_locator(LinearLocator(10))
    ax.zaxis.set_major_formatter(FormatStrFormatter('%.02f'))
    cset = ax.contour(x, y, h, zdir='x', offset=0 , cmap=cm.coolwarm)
    cset = ax.contour(x, y, h, zdir='y', offset=n , cmap=cm.coolwarm)
    cset = ax.contour(x, y, h, zdir='z', offset=.5, cmap=cm.coolwarm)

plotset()

surf = ax.plot_surface(x, y, h,rstride=1, cstride=1,cmap=cm.coolwarm,linewidth=0,antialiased=False, alpha=0.7)

fig.colorbar(surf, shrink=0.5, aspect=5)


from matplotlib import animation


def data(k,h,surf):
    proses()
    ax.clear()
    plotset()
    surf = ax.plot_surface(x, y, h,rstride=1, cstride=1,cmap=cm.coolwarm,linewidth=0,antialiased=False, alpha=0.7)
    return surf,

ani = animation.FuncAnimation(fig, data, fargs=(h,surf), interval=10, blit=False)
#ani.save('air.mp4', bitrate=512)
plt.show()